
import { Link, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

/**
 * Komponen navigasi utama aplikasi
 * - Menampilkan logo dan menu navigasi
 * - Highlight halaman aktif
 * - Responsive design dengan efek hover
 */
const Navigation = () => {
  const location = useLocation();

  return (
    <nav className="relative z-50 bg-black/20 backdrop-blur-md border-b border-white/10">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-pink-500 to-purple-500 rounded-lg flex items-center justify-center shine-effect">
              <span className="text-white font-bold text-xl">🎤</span>
            </div>
            <span className="text-white font-bold text-xl">KaraokeWeb</span>
          </Link>

          {/* Menu Navigasi */}
          <div className="flex items-center space-x-4">
            <Link to="/">
              <Button
                variant={location.pathname === "/" ? "default" : "ghost"}
                className={cn(
                  "text-white transition-all duration-300",
                  location.pathname === "/" 
                    ? "bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600" 
                    : "hover:bg-white/20"
                )}
              >
                Beranda
              </Button>
            </Link>
            <Link to="/karaoke">
              <Button
                variant={location.pathname === "/karaoke" ? "default" : "ghost"}
                className={cn(
                  "text-white transition-all duration-300",
                  location.pathname === "/karaoke" 
                    ? "bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600" 
                    : "hover:bg-white/20"
                )}
              >
                Karaoke
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;
