export interface YouTubeVideo {
  id: string;
  title: string;
  thumbnail: string;
  channelTitle: string;
  publishedAt: string;
  description: string;
  duration?: string;
}

export interface Playlist {
  id: string;
  name: string;
  description?: string;
  user_id?: string;
  created_at: string;
  updated_at: string;
  is_public: boolean;
  thumbnail_url?: string;
  videos?: PlaylistVideo[];
  video_count?: number;
}

export interface PlaylistVideo {
  id: string;
  playlist_id: string;
  video_id: string;
  video_title: string;
  video_thumbnail: string;
  channel_title: string;
  duration?: string;
  position: number;
  added_at: string;
}

export interface CreatePlaylistData {
  name: string;
  description?: string;
  is_public?: boolean;
}

export interface UpdatePlaylistData {
  name?: string;
  description?: string;
  is_public?: boolean;
  thumbnail_url?: string;
}

export interface AddVideoToPlaylistData {
  playlist_id: string;
  video: YouTubeVideo;
  position?: number;
}

export interface PlaylistContextType {
  playlists: Playlist[];
  currentPlaylist: Playlist | null;
  currentVideoIndex: number;
  isPlaying: boolean;
  isShuffled: boolean;
  isRepeating: boolean;
  loading: boolean;
  error: string | null;
  
  // Playlist management
  createPlaylist: (data: CreatePlaylistData) => Promise<Playlist>;
  updatePlaylist: (id: string, data: UpdatePlaylistData) => Promise<void>;
  deletePlaylist: (id: string) => Promise<void>;
  loadPlaylists: () => Promise<void>;
  
  // Video management
  addVideoToPlaylist: (data: AddVideoToPlaylistData) => Promise<void>;
  removeVideoFromPlaylist: (playlistId: string, videoId: string) => Promise<void>;
  reorderPlaylistVideos: (playlistId: string, videoIds: string[]) => Promise<void>;
  
  // Playback control
  playPlaylist: (playlist: Playlist, startIndex?: number) => void;
  playNext: () => void;
  playPrevious: () => void;
  toggleShuffle: () => void;
  toggleRepeat: () => void;
  setCurrentVideo: (index: number) => void;
}

export enum PlaylistRepeatMode {
  OFF = 'off',
  ALL = 'all',
  ONE = 'one'
}

export interface PlaylistSettings {
  autoplay: boolean;
  shuffle: boolean;
  repeat: PlaylistRepeatMode;
  volume: number;
}
