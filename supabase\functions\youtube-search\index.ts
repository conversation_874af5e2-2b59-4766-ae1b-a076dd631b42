
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { query } = await req.json();
    const youtubeApiKey = Deno.env.get('YOUTUBE_API_KEY');

    if (!youtubeApiKey) {
      throw new Error('YouTube API key not found');
    }

    // Search for karaoke videos
    const searchQuery = `${query} karaoke instrumental minus one`;
    const searchUrl = `https://www.googleapis.com/youtube/v3/search?part=snippet&maxResults=20&q=${encodeURIComponent(searchQuery)}&type=video&key=${youtubeApiKey}`;

    console.log('Searching YouTube for:', searchQuery);

    const response = await fetch(searchUrl);
    const data = await response.json();

    if (!response.ok) {
      throw new Error(`YouTube API error: ${data.error?.message || 'Unknown error'}`);
    }

    // Filter results to focus on karaoke videos
    const karaokeVideos = data.items?.filter((item: any) => {
      const title = item.snippet.title.toLowerCase();
      const description = item.snippet.description.toLowerCase();
      
      // Check if it contains karaoke-related keywords
      const karaokeKeywords = ['karaoke', 'instrumental', 'minus one', 'backing track', 'no vocals'];
      const hasKaraokeKeyword = karaokeKeywords.some(keyword => 
        title.includes(keyword) || description.includes(keyword)
      );

      // Exclude covers or live performances
      const excludeKeywords = ['cover', 'live', 'concert', 'official music video'];
      const hasExcludeKeyword = excludeKeywords.some(keyword => title.includes(keyword));

      return hasKaraokeKeyword && !hasExcludeKeyword;
    }) || [];

    // Format the results
    const formattedResults = karaokeVideos.map((item: any) => ({
      id: item.id.videoId,
      title: item.snippet.title,
      thumbnail: item.snippet.thumbnails.medium?.url || item.snippet.thumbnails.default?.url,
      channelTitle: item.snippet.channelTitle,
      publishedAt: item.snippet.publishedAt,
      description: item.snippet.description
    }));

    console.log(`Found ${formattedResults.length} karaoke videos`);

    return new Response(JSON.stringify({ videos: formattedResults }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error in youtube-search function:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
