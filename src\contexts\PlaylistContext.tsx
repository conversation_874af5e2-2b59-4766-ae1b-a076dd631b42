import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';
import { PlaylistService } from '@/services/playlistService';
import type {
  Playlist,
  PlaylistContextType,
  CreatePlaylistData,
  UpdatePlaylistData,
  AddVideoToPlaylistData
} from '@/types/playlist';

// Import the enum directly
import { PlaylistRepeatMode } from '@/types/playlist';

interface PlaylistState {
  playlists: Playlist[];
  currentPlaylist: Playlist | null;
  currentVideoIndex: number;
  isPlaying: boolean;
  isShuffled: boolean;
  repeatMode: PlaylistRepeatMode;
  loading: boolean;
  error: string | null;
}

type PlaylistAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_PLAYLISTS'; payload: Playlist[] }
  | { type: 'ADD_PLAYLIST'; payload: Playlist }
  | { type: 'UPDATE_PLAYLIST'; payload: { id: string; data: Partial<Playlist> } }
  | { type: 'DELETE_PLAYLIST'; payload: string }
  | { type: 'SET_CURRENT_PLAYLIST'; payload: { playlist: Playlist; index?: number } }
  | { type: 'SET_CURRENT_VIDEO_INDEX'; payload: number }
  | { type: 'SET_PLAYING'; payload: boolean }
  | { type: 'TOGGLE_SHUFFLE' }
  | { type: 'SET_REPEAT_MODE'; payload: PlaylistRepeatMode }
  | { type: 'ADD_VIDEO_TO_PLAYLIST'; payload: { playlistId: string; video: any } }
  | { type: 'REMOVE_VIDEO_FROM_PLAYLIST'; payload: { playlistId: string; videoId: string } };

const initialState: PlaylistState = {
  playlists: [],
  currentPlaylist: null,
  currentVideoIndex: 0,
  isPlaying: false,
  isShuffled: false,
  repeatMode: PlaylistRepeatMode.OFF,
  loading: false,
  error: null,
};

function playlistReducer(state: PlaylistState, action: PlaylistAction): PlaylistState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    
    case 'SET_PLAYLISTS':
      return { ...state, playlists: action.payload, loading: false };
    
    case 'ADD_PLAYLIST':
      return { ...state, playlists: [action.payload, ...state.playlists] };
    
    case 'UPDATE_PLAYLIST':
      return {
        ...state,
        playlists: state.playlists.map(p => 
          p.id === action.payload.id ? { ...p, ...action.payload.data } : p
        ),
        currentPlaylist: state.currentPlaylist?.id === action.payload.id 
          ? { ...state.currentPlaylist, ...action.payload.data }
          : state.currentPlaylist
      };
    
    case 'DELETE_PLAYLIST':
      return {
        ...state,
        playlists: state.playlists.filter(p => p.id !== action.payload),
        currentPlaylist: state.currentPlaylist?.id === action.payload ? null : state.currentPlaylist
      };
    
    case 'SET_CURRENT_PLAYLIST':
      return {
        ...state,
        currentPlaylist: action.payload.playlist,
        currentVideoIndex: action.payload.index ?? 0,
        isPlaying: false
      };
    
    case 'SET_CURRENT_VIDEO_INDEX':
      return { ...state, currentVideoIndex: action.payload };
    
    case 'SET_PLAYING':
      return { ...state, isPlaying: action.payload };
    
    case 'TOGGLE_SHUFFLE':
      return { ...state, isShuffled: !state.isShuffled };
    
    case 'SET_REPEAT_MODE':
      return { ...state, repeatMode: action.payload };
    
    case 'ADD_VIDEO_TO_PLAYLIST':
      return {
        ...state,
        playlists: state.playlists.map(p => 
          p.id === action.payload.playlistId 
            ? { ...p, video_count: (p.video_count || 0) + 1 }
            : p
        )
      };
    
    case 'REMOVE_VIDEO_FROM_PLAYLIST':
      return {
        ...state,
        playlists: state.playlists.map(p => 
          p.id === action.payload.playlistId 
            ? { ...p, video_count: Math.max((p.video_count || 0) - 1, 0) }
            : p
        )
      };
    
    default:
      return state;
  }
}

const PlaylistContext = createContext<PlaylistContextType | undefined>(undefined);

export function PlaylistProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(playlistReducer, initialState);

  // Load playlists
  const loadPlaylists = useCallback(async () => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });

    try {
      console.log('Loading playlists...');
      const playlists = await PlaylistService.getPlaylists();
      console.log('Playlists loaded:', playlists);
      dispatch({ type: 'SET_PLAYLISTS', payload: playlists });
    } catch (error) {
      console.error('Failed to load playlists:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
    }
  }, []);

  // Create playlist
  const createPlaylist = useCallback(async (data: CreatePlaylistData): Promise<Playlist> => {
    try {
      const playlist = await PlaylistService.createPlaylist(data);
      dispatch({ type: 'ADD_PLAYLIST', payload: playlist });
      return playlist;
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: (error as Error).message });
      throw error;
    }
  }, []);

  // Update playlist
  const updatePlaylist = useCallback(async (id: string, data: UpdatePlaylistData) => {
    try {
      await PlaylistService.updatePlaylist(id, data);
      dispatch({ type: 'UPDATE_PLAYLIST', payload: { id, data } });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: (error as Error).message });
      throw error;
    }
  }, []);

  // Delete playlist
  const deletePlaylist = useCallback(async (id: string) => {
    try {
      await PlaylistService.deletePlaylist(id);
      dispatch({ type: 'DELETE_PLAYLIST', payload: id });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: (error as Error).message });
      throw error;
    }
  }, []);

  // Add video to playlist
  const addVideoToPlaylist = useCallback(async (data: AddVideoToPlaylistData) => {
    try {
      await PlaylistService.addVideoToPlaylist(data);
      dispatch({ 
        type: 'ADD_VIDEO_TO_PLAYLIST', 
        payload: { playlistId: data.playlist_id, video: data.video } 
      });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: (error as Error).message });
      throw error;
    }
  }, []);

  // Remove video from playlist
  const removeVideoFromPlaylist = useCallback(async (playlistId: string, videoId: string) => {
    try {
      await PlaylistService.removeVideoFromPlaylist(playlistId, videoId);
      dispatch({ 
        type: 'REMOVE_VIDEO_FROM_PLAYLIST', 
        payload: { playlistId, videoId } 
      });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: (error as Error).message });
      throw error;
    }
  }, []);

  // Reorder playlist videos
  const reorderPlaylistVideos = useCallback(async (playlistId: string, videoIds: string[]) => {
    try {
      await PlaylistService.reorderPlaylistVideos(playlistId, videoIds);
      // Reload the specific playlist to get updated order
      const updatedPlaylist = await PlaylistService.getPlaylistById(playlistId);
      if (updatedPlaylist) {
        dispatch({ type: 'UPDATE_PLAYLIST', payload: { id: playlistId, data: updatedPlaylist } });
      }
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: (error as Error).message });
      throw error;
    }
  }, []);

  // Playback controls
  const playPlaylist = useCallback((playlist: Playlist, startIndex = 0) => {
    dispatch({ type: 'SET_CURRENT_PLAYLIST', payload: { playlist, index: startIndex } });
    dispatch({ type: 'SET_PLAYING', payload: true });
  }, []);

  const playNext = useCallback(() => {
    if (!state.currentPlaylist?.videos?.length) return;
    
    const nextIndex = (state.currentVideoIndex + 1) % state.currentPlaylist.videos.length;
    dispatch({ type: 'SET_CURRENT_VIDEO_INDEX', payload: nextIndex });
  }, [state.currentPlaylist, state.currentVideoIndex]);

  const playPrevious = useCallback(() => {
    if (!state.currentPlaylist?.videos?.length) return;
    
    const prevIndex = state.currentVideoIndex === 0 
      ? state.currentPlaylist.videos.length - 1 
      : state.currentVideoIndex - 1;
    dispatch({ type: 'SET_CURRENT_VIDEO_INDEX', payload: prevIndex });
  }, [state.currentPlaylist, state.currentVideoIndex]);

  const toggleShuffle = useCallback(() => {
    dispatch({ type: 'TOGGLE_SHUFFLE' });
  }, []);

  const toggleRepeat = useCallback(() => {
    const modes = [PlaylistRepeatMode.OFF, PlaylistRepeatMode.ALL, PlaylistRepeatMode.ONE];
    const currentIndex = modes.indexOf(state.repeatMode);
    const nextMode = modes[(currentIndex + 1) % modes.length];
    dispatch({ type: 'SET_REPEAT_MODE', payload: nextMode });
  }, [state.repeatMode]);

  const setCurrentVideo = useCallback((index: number) => {
    dispatch({ type: 'SET_CURRENT_VIDEO_INDEX', payload: index });
  }, []);

  // Load playlists on mount
  useEffect(() => {
    loadPlaylists();
  }, [loadPlaylists]);

  const contextValue: PlaylistContextType = {
    playlists: state.playlists,
    currentPlaylist: state.currentPlaylist,
    currentVideoIndex: state.currentVideoIndex,
    isPlaying: state.isPlaying,
    isShuffled: state.isShuffled,
    isRepeating: state.repeatMode !== PlaylistRepeatMode.OFF,
    loading: state.loading,
    error: state.error,
    
    createPlaylist,
    updatePlaylist,
    deletePlaylist,
    loadPlaylists,
    
    addVideoToPlaylist,
    removeVideoFromPlaylist,
    reorderPlaylistVideos,
    
    playPlaylist,
    playNext,
    playPrevious,
    toggleShuffle,
    toggleRepeat,
    setCurrentVideo,
  };

  return (
    <PlaylistContext.Provider value={contextValue}>
      {children}
    </PlaylistContext.Provider>
  );
}

export function usePlaylist() {
  const context = useContext(PlaylistContext);
  if (context === undefined) {
    throw new Error('usePlaylist must be used within a PlaylistProvider');
  }
  return context;
}
