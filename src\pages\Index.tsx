
import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import WaveVisualizer from "@/components/WaveVisualizer";

/**
 * Halaman utama aplikasi karaoke
 * - Hero section dengan animasi gelombang suara
 * - Call-to-action untuk memulai karaoke
 * - Fitur unggulan aplikasi
 * - Desain responsif dengan gradien dan animasi
 */
const Index = () => {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 px-4 text-center">
        <div className="container mx-auto max-w-4xl">
          {/* Animated Background Elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute top-20 left-10 w-20 h-20 bg-pink-500/20 rounded-full blur-xl float-animation"></div>
            <div className="absolute top-40 right-20 w-32 h-32 bg-purple-500/20 rounded-full blur-xl float-animation" style={{animationDelay: '1s'}}></div>
            <div className="absolute bottom-20 left-1/4 w-24 h-24 bg-blue-500/20 rounded-full blur-xl float-animation" style={{animationDelay: '2s'}}></div>
          </div>

          {/* Content */}
          <div className="relative z-10">
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 bg-gradient-to-r from-pink-400 via-purple-400 to-blue-400 bg-clip-text text-transparent">
              Sing Along
            </h1>
            <p className="text-xl md:text-2xl text-white/80 mb-8 max-w-2xl mx-auto">
              Rasakan pengalaman karaoke terbaik dengan koleksi lagu terlengkap dan fitur interaktif yang memukau
            </p>
            
            {/* Wave Visualizer */}
            <div className="mb-8">
              <WaveVisualizer />
            </div>

            <Link to="/karaoke">
              <Button size="lg" className="bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600 text-white font-semibold px-8 py-6 text-lg rounded-full shine-effect transition-all duration-300 transform hover:scale-105">
                🎤 Mulai Bernyanyi
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 px-4">
        <div className="container mx-auto max-w-6xl">
          <h2 className="text-3xl md:text-4xl font-bold text-white text-center mb-12">
            Fitur Unggulan
          </h2>
          
          <div className="grid md:grid-cols-3 gap-8">
            <Card className="bg-white/10 backdrop-blur-md border-white/20 hover:bg-white/20 transition-all duration-300">
              <CardContent className="p-6 text-center">
                <div className="text-4xl mb-4">🎵</div>
                <h3 className="text-xl font-semibold text-white mb-2">Koleksi Lagu Lengkap</h3>
                <p className="text-white/70">Ribuan lagu dari berbagai genre dan era tersedia untuk Anda</p>
              </CardContent>
            </Card>

            <Card className="bg-white/10 backdrop-blur-md border-white/20 hover:bg-white/20 transition-all duration-300">
              <CardContent className="p-6 text-center">
                <div className="text-4xl mb-4">🎤</div>
                <h3 className="text-xl font-semibold text-white mb-2">Kualitas Audio HD</h3>
                <p className="text-white/70">Nikmati pengalaman bernyanyi dengan kualitas suara terbaik</p>
              </CardContent>
            </Card>

            <Card className="bg-white/10 backdrop-blur-md border-white/20 hover:bg-white/20 transition-all duration-300">
              <CardContent className="p-6 text-center">
                <div className="text-4xl mb-4">🌟</div>
                <h3 className="text-xl font-semibold text-white mb-2">Interface Modern</h3>
                <p className="text-white/70">Desain yang elegan dan mudah digunakan untuk semua kalangan</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Index;
