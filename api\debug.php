<?php
/**
 * Debug API Endpoint
 * Check if API is working and accessible
 */

// Set CORS headers
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-User-ID, X-Requested-With");
header("Content-Type: application/json");

// Handle preflight OPTIONS requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit(0);
}

// Basic debug info
$debug_info = [
    'status' => 'API is working',
    'timestamp' => date('Y-m-d H:i:s'),
    'method' => $_SERVER['REQUEST_METHOD'],
    'php_version' => phpversion(),
    'server_info' => [
        'server_name' => $_SERVER['SERVER_NAME'] ?? 'unknown',
        'server_port' => $_SERVER['SERVER_PORT'] ?? 'unknown',
        'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
        'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'unknown'
    ],
    'headers' => [
        'origin' => $_SERVER['HTTP_ORIGIN'] ?? 'not set',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'not set',
        'content_type' => $_SERVER['CONTENT_TYPE'] ?? 'not set'
    ]
];

// Test database connection
try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        $debug_info['database'] = 'Connected successfully';
        
        // Test if tables exist
        $tables = ['users', 'playlists', 'playlist_videos'];
        $existing_tables = [];
        
        foreach ($tables as $table) {
            try {
                $stmt = $db->prepare("SELECT COUNT(*) FROM $table");
                $stmt->execute();
                $count = $stmt->fetchColumn();
                $existing_tables[$table] = "exists (rows: $count)";
            } catch (Exception $e) {
                $existing_tables[$table] = "error: " . $e->getMessage();
            }
        }
        
        $debug_info['tables'] = $existing_tables;
    } else {
        $debug_info['database'] = 'Connection failed';
    }
} catch (Exception $e) {
    $debug_info['database'] = 'Error: ' . $e->getMessage();
}

// Check file permissions
$debug_info['files'] = [
    'config_exists' => file_exists('config/database.php') ? 'yes' : 'no',
    'config_readable' => is_readable('config/database.php') ? 'yes' : 'no',
    'playlists_exists' => file_exists('playlists.php') ? 'yes' : 'no',
    'htaccess_exists' => file_exists('.htaccess') ? 'yes' : 'no'
];

http_response_code(200);
echo json_encode($debug_info, JSON_PRETTY_PRINT);
?>
