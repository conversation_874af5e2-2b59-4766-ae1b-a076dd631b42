@echo off
echo ========================================
echo XAMPP Setup for D: Drive
echo ========================================

echo.
echo Checking XAMPP installation...
if not exist "D:\XAMPP\htdocs" (
    echo ERROR: D:\XAMPP\htdocs not found!
    echo Please check your XAMPP installation path.
    pause
    exit /b 1
)

echo ✅ XAMPP found at D:\XAMPP\

echo.
echo Creating project folder...
if not exist "D:\XAMPP\htdocs\sing-along-web-beats" (
    mkdir "D:\XAMPP\htdocs\sing-along-web-beats"
    echo ✅ Created: D:\XAMPP\htdocs\sing-along-web-beats
) else (
    echo ✅ Folder exists: D:\XAMPP\htdocs\sing-along-web-beats
)

echo.
echo Creating API folder...
if not exist "D:\XAMPP\htdocs\sing-along-web-beats\api" (
    mkdir "D:\XAMPP\htdocs\sing-along-web-beats\api"
    echo ✅ Created: D:\XAMPP\htdocs\sing-along-web-beats\api
) else (
    echo ✅ Folder exists: D:\XAMPP\htdocs\sing-along-web-beats\api
)

echo.
echo Creating config folder...
if not exist "D:\XAMPP\htdocs\sing-along-web-beats\api\config" (
    mkdir "D:\XAMPP\htdocs\sing-along-web-beats\api\config"
    echo ✅ Created: D:\XAMPP\htdocs\sing-along-web-beats\api\config
) else (
    echo ✅ Folder exists: D:\XAMPP\htdocs\sing-along-web-beats\api\config
)

echo.
echo ========================================
echo Setup completed!
echo ========================================
echo.
echo Next steps:
echo 1. Copy API files to: D:\XAMPP\htdocs\sing-along-web-beats\api\
echo 2. Start Apache and MySQL in XAMPP Control Panel
echo 3. Test: http://localhost/sing-along-web-beats/api/debug.php
echo.
pause
