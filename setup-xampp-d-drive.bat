@echo off
echo ========================================
echo XAMP Setup for D: Drive
echo ========================================

echo.
echo Checking XAMP installation...
if not exist "D:\XAMP\htdocs" (
    echo ERROR: D:\XAMP\htdocs not found!
    echo Please check your XAMP installation path.
    pause
    exit /b 1
)

echo ✅ XAMP found at D:\XAMP\

echo.
echo Creating project folder...
if not exist "D:\XAMP\htdocs\sing-along-web-beats" (
    mkdir "D:\XAMP\htdocs\sing-along-web-beats"
    echo ✅ Created: D:\XAMP\htdocs\sing-along-web-beats
) else (
    echo ✅ Folder exists: D:\XAMP\htdocs\sing-along-web-beats
)

echo.
echo Creating API folder...
if not exist "D:\XAMP\htdocs\sing-along-web-beats\api" (
    mkdir "D:\XAMP\htdocs\sing-along-web-beats\api"
    echo ✅ Created: D:\XAMP\htdocs\sing-along-web-beats\api
) else (
    echo ✅ Folder exists: D:\XAMP\htdocs\sing-along-web-beats\api
)

echo.
echo Creating config folder...
if not exist "D:\XAMP\htdocs\sing-along-web-beats\api\config" (
    mkdir "D:\XAMP\htdocs\sing-along-web-beats\api\config"
    echo ✅ Created: D:\XAMP\htdocs\sing-along-web-beats\api\config
) else (
    echo ✅ Folder exists: D:\XAMP\htdocs\sing-along-web-beats\api\config
)

echo.
echo ========================================
echo Setup completed!
echo ========================================
echo.
echo Next steps:
echo 1. Copy API files to: D:\XAMP\htdocs\sing-along-web-beats\api\
echo 2. Start Apache and MySQL in XAMP Control Panel
echo 3. Test: http://localhost/sing-along-web-beats/api/debug.php
echo.
pause
