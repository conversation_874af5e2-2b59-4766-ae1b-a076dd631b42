# API Routing for Karaoke Playlist Application
# Enable URL rewriting
RewriteEngine On

# CORS Headers - Set before any other processing
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-User-ID, X-Requested-With"
Header always set Access-Control-Max-Age "86400"

# Handle preflight OPTIONS requests immediately
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ - [R=200,L]

# API Routes
# CORS test
RewriteRule ^test-cors/?$ test-cors.php [L,QSA]

# Playlist routes
RewriteRule ^playlists/?$ playlists.php [L,QSA]
RewriteRule ^playlists/([0-9]+)/?$ playlists.php?id=$1 [L,QSA]

# Playlist videos routes
RewriteRule ^playlist-videos/?$ playlist-videos.php [L,QSA]
RewriteRule ^playlists/([0-9]+)/videos/?$ playlist-videos.php?playlist_id=$1 [L,QSA]
RewriteRule ^playlists/([0-9]+)/videos/([^/]+)/?$ playlist-videos.php?playlist_id=$1&video_id=$2 [L,QSA]

# Test connection
RewriteRule ^test/?$ test-connection.php [L,QSA]

# Error handling
ErrorDocument 404 "API endpoint not found"
ErrorDocument 500 "Internal server error"

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"

# Disable directory browsing
Options -Indexes

# Protect sensitive files
<Files "*.php">
    # Allow PHP files to be executed
</Files>

<Files "config/*">
    Order Allow,Deny
    Deny from all
</Files>

# Cache control for API responses
<IfModule mod_expires.c>
    ExpiresActive Off
</IfModule>

<IfModule mod_headers.c>
    Header set Cache-Control "no-cache, no-store, must-revalidate"
    Header set Pragma "no-cache"
    Header set Expires 0
</IfModule>
