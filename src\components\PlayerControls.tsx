
import { But<PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Play, Pause, SkipBack, SkipForward } from "lucide-react";

interface PlayerControlsProps {
  isPlaying: boolean;
  onPlayToggle: () => void;
  currentTime: number;
  onTimeChange: (time: number) => void;
  isDarkMode: boolean;
}

/**
 * Komponen kontrol pemutar karaoke
 * - Play/pause button
 * - Progress slider
 * - Skip controls
 * - Time display
 */
const PlayerControls = ({ 
  isPlaying, 
  onPlayToggle, 
  currentTime, 
  onTimeChange,
  isDarkMode 
}: PlayerControlsProps) => {
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleSkipBack = () => {
    onTimeChange(Math.max(0, currentTime - 10));
  };

  const handleSkipForward = () => {
    onTimeChange(currentTime + 10);
  };

  return (
    <div className="space-y-4 mt-4">
      {/* Progress Bar */}
      <div className="space-y-2">
        <Slider
          value={[currentTime]}
          onValueChange={(value) => onTimeChange(value[0])}
          max={300} // 5 menit sebagai contoh
          step={0.1}
          className="w-full"
        />
        <div className={`flex justify-between text-xs ${isDarkMode ? 'text-white/70' : 'text-gray-700'}`}>
          <span>{formatTime(currentTime)}</span>
          <span>05:00</span>
        </div>
      </div>

      {/* Control Buttons */}
      <div className="flex items-center justify-center space-x-4">
        <Button
          onClick={handleSkipBack}
          variant="outline"
          size="icon"
          className={`${isDarkMode ? 'bg-white/10 border-white/20 text-white hover:bg-white/20' : 'bg-black/10 border-black/20 text-black hover:bg-black/20'}`}
        >
          <SkipBack className="w-4 h-4" />
        </Button>
        
        <Button
          onClick={onPlayToggle}
          size="icon"
          className="bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600 w-12 h-12"
        >
          {isPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
        </Button>
        
        <Button
          onClick={handleSkipForward}
          variant="outline"
          size="icon"
          className={`${isDarkMode ? 'bg-white/10 border-white/20 text-white hover:bg-white/20' : 'bg-black/10 border-black/20 text-black hover:bg-black/20'}`}
        >
          <SkipForward className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
};

export default PlayerControls;
