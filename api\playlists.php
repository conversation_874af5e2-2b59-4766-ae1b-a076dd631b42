<?php
/**
 * Playlist API Endpoints
 * Handles CRUD operations for playlists
 */

require_once 'config/database.php';

// Set CORS headers
setCorsHeaders();

// Get database connection
$database = new Database();
$db = $database->getConnection();

if (!$db) {
    errorResponse('Database connection failed', 500);
}

// Get request method and data
$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);
$user_id = getCurrentUserId();

// Log the request
logRequest('/api/playlists', $method, $user_id);

switch ($method) {
    case 'GET':
        getPlaylists($db, $user_id);
        break;
    case 'POST':
        createPlaylist($db, $input, $user_id);
        break;
    case 'PUT':
        updatePlaylist($db, $input, $user_id);
        break;
    case 'DELETE':
        deletePlaylist($db, $user_id);
        break;
    default:
        errorResponse('Method not allowed', 405);
}

/**
 * Get user playlists
 */
function getPlaylists($db, $user_id) {
    try {
        $query = "
            SELECT 
                p.id,
                p.uuid,
                p.name,
                p.description,
                p.user_id,
                p.created_at,
                p.updated_at,
                p.is_public,
                p.thumbnail_url,
                p.video_count,
                p.total_duration,
                u.username,
                u.full_name
            FROM playlists p
            JOIN users u ON p.user_id = u.id
            WHERE p.user_id = :user_id OR p.is_public = 1
            ORDER BY p.updated_at DESC
        ";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        
        $playlists = $stmt->fetchAll();
        
        // Get videos for each playlist
        foreach ($playlists as &$playlist) {
            $playlist['videos'] = getPlaylistVideos($db, $playlist['id']);
        }
        
        successResponse($playlists);
        
    } catch (Exception $e) {
        errorResponse('Failed to fetch playlists: ' . $e->getMessage(), 500);
    }
}

/**
 * Create new playlist
 */
function createPlaylist($db, $input, $user_id) {
    try {
        validateRequired($input, ['name']);
        
        $name = sanitizeInput($input['name']);
        $description = isset($input['description']) ? sanitizeInput($input['description']) : null;
        $is_public = isset($input['is_public']) ? (bool)$input['is_public'] : false;
        $uuid = generateUUID();
        
        $query = "
            INSERT INTO playlists (uuid, name, description, user_id, is_public)
            VALUES (:uuid, :name, :description, :user_id, :is_public)
        ";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':uuid', $uuid);
        $stmt->bindParam(':name', $name);
        $stmt->bindParam(':description', $description);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':is_public', $is_public, PDO::PARAM_BOOL);
        
        if ($stmt->execute()) {
            $playlist_id = $db->lastInsertId();
            
            // Get the created playlist
            $query = "SELECT * FROM playlists WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':id', $playlist_id);
            $stmt->execute();
            
            $playlist = $stmt->fetch();
            $playlist['videos'] = [];
            
            successResponse($playlist, 'Playlist created successfully');
        } else {
            errorResponse('Failed to create playlist', 500);
        }
        
    } catch (Exception $e) {
        errorResponse('Failed to create playlist: ' . $e->getMessage(), 500);
    }
}

/**
 * Update playlist
 */
function updatePlaylist($db, $input, $user_id) {
    try {
        validateRequired($input, ['id']);
        
        $playlist_id = (int)$input['id'];
        checkPlaylistOwnership($playlist_id, $user_id, $db);
        
        $updates = [];
        $params = [':id' => $playlist_id];
        
        if (isset($input['name'])) {
            $updates[] = "name = :name";
            $params[':name'] = sanitizeInput($input['name']);
        }
        
        if (isset($input['description'])) {
            $updates[] = "description = :description";
            $params[':description'] = sanitizeInput($input['description']);
        }
        
        if (isset($input['is_public'])) {
            $updates[] = "is_public = :is_public";
            $params[':is_public'] = (bool)$input['is_public'];
        }
        
        if (isset($input['thumbnail_url'])) {
            $updates[] = "thumbnail_url = :thumbnail_url";
            $params[':thumbnail_url'] = sanitizeInput($input['thumbnail_url']);
        }
        
        if (empty($updates)) {
            errorResponse('No fields to update', 400);
        }
        
        $query = "UPDATE playlists SET " . implode(', ', $updates) . " WHERE id = :id";
        $stmt = $db->prepare($query);
        
        if ($stmt->execute($params)) {
            successResponse(null, 'Playlist updated successfully');
        } else {
            errorResponse('Failed to update playlist', 500);
        }
        
    } catch (Exception $e) {
        errorResponse('Failed to update playlist: ' . $e->getMessage(), 500);
    }
}

/**
 * Delete playlist
 */
function deletePlaylist($db, $user_id) {
    try {
        $playlist_id = isset($_GET['id']) ? (int)$_GET['id'] : null;
        
        if (!$playlist_id) {
            errorResponse('Playlist ID is required', 400);
        }
        
        checkPlaylistOwnership($playlist_id, $user_id, $db);
        
        // Delete playlist (videos will be deleted by CASCADE)
        $query = "DELETE FROM playlists WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $playlist_id);
        
        if ($stmt->execute()) {
            successResponse(null, 'Playlist deleted successfully');
        } else {
            errorResponse('Failed to delete playlist', 500);
        }
        
    } catch (Exception $e) {
        errorResponse('Failed to delete playlist: ' . $e->getMessage(), 500);
    }
}

/**
 * Get videos for a playlist
 */
function getPlaylistVideos($db, $playlist_id) {
    try {
        $query = "
            SELECT 
                id,
                uuid,
                video_id,
                video_title,
                video_thumbnail,
                channel_title,
                duration,
                duration_seconds,
                position,
                added_at
            FROM playlist_videos
            WHERE playlist_id = :playlist_id
            ORDER BY position ASC
        ";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':playlist_id', $playlist_id);
        $stmt->execute();
        
        return $stmt->fetchAll();
        
    } catch (Exception $e) {
        return [];
    }
}
?>
