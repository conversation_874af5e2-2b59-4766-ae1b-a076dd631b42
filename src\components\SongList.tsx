
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { useState, useEffect } from "react";

// Data contoh lagu-lagu populer
const sampleSongs = [
  { id: 1, title: "Bohemian Rhapsody", artist: "Queen", duration: "5:55", genre: "Rock" },
  { id: 2, title: "Imagine", artist: "<PERSON> Lennon", duration: "3:03", genre: "Pop" },
  { id: 3, title: "Hotel California", artist: "Eagles", duration: "6:30", genre: "Rock" },
  { id: 4, title: "Yesterday", artist: "The Beatles", duration: "2:05", genre: "Pop" },
  { id: 5, title: "My Way", artist: "Frank Sinatra", duration: "4:35", genre: "Jazz" },
  { id: 6, title: "Sweet Child O' Mine", artist: "Guns N' Roses", duration: "5:03", genre: "Rock" },
  { id: 7, title: "<PERSON>", artist: "<PERSON>", duration: "4:54", genre: "Pop" },
  { id: 8, title: "Stairway to Heaven", artist: "Led Zeppelin", duration: "8:02", genre: "Rock" },
];

interface SongListProps {
  searchQuery: string;
  onSongSelect: (song: any) => void;
  selectedSong: any;
}

/**
 * Komponen daftar lagu karaoke
 * - Menampilkan koleksi lagu yang tersedia
 * - Fitur pencarian real-time
 * - Highlight lagu yang dipilih
 * - Informasi lengkap setiap lagu (judul, artis, durasi, genre)
 */
const SongList = ({ searchQuery, onSongSelect, selectedSong }: SongListProps) => {
  const [filteredSongs, setFilteredSongs] = useState(sampleSongs);

  useEffect(() => {
    const filtered = sampleSongs.filter(
      (song) =>
        song.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        song.artist.toLowerCase().includes(searchQuery.toLowerCase()) ||
        song.genre.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredSongs(filtered);
  }, [searchQuery]);

  return (
    <div className="space-y-3 max-h-96 overflow-y-auto">
      {filteredSongs.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-white/60">Tidak ada lagu yang ditemukan</p>
        </div>
      ) : (
        filteredSongs.map((song) => (
          <Card 
            key={song.id} 
            className={`cursor-pointer transition-all duration-300 hover:scale-105 ${
              selectedSong?.id === song.id 
                ? 'bg-gradient-to-r from-pink-500/30 to-purple-500/30 border-pink-400' 
                : 'bg-white/5 hover:bg-white/10 border-white/10'
            }`}
            onClick={() => onSongSelect(song)}
          >
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <h3 className="font-semibold text-white text-lg">{song.title}</h3>
                  <p className="text-white/70">{song.artist}</p>
                  <div className="flex items-center space-x-4 mt-1">
                    <span className="text-sm text-white/60">{song.duration}</span>
                    <span className="text-sm text-white/60 bg-white/10 px-2 py-1 rounded">
                      {song.genre}
                    </span>
                  </div>
                </div>
                <Button 
                  size="sm" 
                  className="ml-4 bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600"
                >
                  {selectedSong?.id === song.id ? '🎵 Playing' : '▶️ Play'}
                </Button>
              </div>
            </CardContent>
          </Card>
        ))
      )}
    </div>
  );
};

export default SongList;
