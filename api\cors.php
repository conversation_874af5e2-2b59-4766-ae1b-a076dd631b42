<?php
/**
 * CORS Handler
 * Handle CORS preflight requests
 */

// Set CORS headers
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-User-ID, X-Requested-With");
header("Access-Control-Max-Age: 86400");

// Handle OPTIONS preflight request
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit(0);
}

// For non-OPTIONS requests, just return success
http_response_code(200);
echo json_encode(['status' => 'CORS OK']);
?>
