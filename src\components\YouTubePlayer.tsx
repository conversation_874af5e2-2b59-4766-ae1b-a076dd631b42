
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Play, Pause, SkipBack } from "lucide-react";

interface YouTubeVideo {
  id: string;
  title: string;
  thumbnail: string;
  channelTitle: string;
  publishedAt: string;
  description: string;
}

interface YouTubePlayerProps {
  selectedVideo: YouTubeVideo | null;
}

/**
 * Komponen pemutar video YouTube
 * - Embed YouTube player untuk video karaoke
 * - Ko<PERSON>rol dasar dan informasi video
 * - Responsive design
 */
const YouTubePlayer = ({ selectedVideo }: YouTubePlayerProps) => {
  const [isPlaying, setIsPlaying] = useState(false);

  useEffect(() => {
    if (selectedVideo) {
      setIsPlaying(true);
    }
  }, [selectedVideo]);

  if (!selectedVideo) {
    return (
      <Card className="bg-white/10 backdrop-blur-md border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl">Player YouTube</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full flex items-center justify-center">
              <Play className="w-8 h-8 text-white" />
            </div>
            <p className="text-white/60">Pilih video karaoke untuk mulai memutar</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-white/10 backdrop-blur-md border-white/20">
      <CardHeader>
        <CardTitle className="text-white text-xl">Now Playing</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* YouTube Embed */}
        <div className="aspect-video rounded-lg overflow-hidden">
          <iframe
            width="100%"
            height="100%"
            src={`https://www.youtube.com/embed/${selectedVideo.id}?autoplay=1`}
            title={selectedVideo.title}
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            className="w-full h-full"
          />
        </div>

        {/* Video Info */}
        <div>
          <h3 className="text-white font-semibold text-lg mb-2 line-clamp-2">
            {selectedVideo.title}
          </h3>
          <p className="text-white/70 text-sm">{selectedVideo.channelTitle}</p>
        </div>

        {/* Basic Controls Info */}
        <div className="flex items-center space-x-2 pt-2">
          <div className="flex items-center space-x-2 text-white/60 text-sm">
            <Play className="w-4 h-4" />
            <span>Gunakan kontrol di video YouTube untuk mengatur pemutaran</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default YouTubePlayer;
