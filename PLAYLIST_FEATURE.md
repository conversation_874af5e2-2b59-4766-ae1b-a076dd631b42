# YouTube Playlist Feature

## Overview
This feature adds comprehensive playlist functionality to the karaoke web application, allowing users to create, manage, and play YouTube video playlists.

## Features Implemented

### 1. Database Schema
- **playlists table**: Stores playlist metadata (name, description, privacy settings)
- **playlist_videos table**: Stores videos within playlists with position ordering
- **Row Level Security (RLS)**: Ensures users can only access their own playlists or public ones
- **Automatic timestamps**: Updates playlist timestamps when videos are modified

### 2. Core Components

#### PlaylistManager
- Create new playlists
- View all user playlists
- Edit playlist details (name, description, privacy)
- Delete playlists
- Search through playlists

#### PlaylistViewer
- View playlist contents
- Play videos from playlist
- Remove videos from playlist
- Playlist controls (play, pause, next, previous)
- Shuffle and repeat modes

#### AddToPlaylistDialog
- Add videos to existing playlists
- Create new playlists on-the-fly
- Search through playlists

### 3. Integration Points

#### YouTubeSearch Component
- Added "Add to Playlist" button for each video
- Opens AddToPlaylistDialog when clicked

#### Karaoke Page
- Added new "Playlist" tab alongside YouTube and Local songs
- Seamless switching between playlist management and viewing
- Integrated with existing video player

### 4. State Management
- **PlaylistContext**: Centralized state management for all playlist operations
- **PlaylistService**: Database operations and API calls
- **Type definitions**: Comprehensive TypeScript interfaces

## Usage

### Creating a Playlist
1. Go to Karaoke page
2. Click on "Playlist" tab
3. Click "Buat Playlist" (Create Playlist)
4. Enter name, description, and privacy settings
5. Click "Buat Playlist" to create

### Adding Videos to Playlist
1. Search for videos in YouTube tab
2. Click "Tambah ke Playlist" (Add to Playlist) on any video
3. Select existing playlist or create new one
4. Video is added to the selected playlist

### Playing Playlists
1. Go to Playlist tab
2. Click on any playlist to view its contents
3. Click play button on playlist or individual videos
4. Use playlist controls for navigation

### Managing Playlists
- **Edit**: Click edit icon on playlist card
- **Delete**: Click delete icon on playlist card
- **Make Public/Private**: Toggle in playlist settings
- **Reorder Videos**: Drag and drop (future enhancement)

## Database Setup

Run the migration file to set up the database:
```sql
-- Run the contents of supabase/migrations/001_create_playlist_tables.sql
```

## File Structure

```
src/
├── components/
│   ├── PlaylistManager.tsx       # Main playlist management
│   ├── PlaylistViewer.tsx        # Playlist viewing and playback
│   ├── AddToPlaylistDialog.tsx   # Add videos to playlists
│   └── YouTubeSearch.tsx         # Updated with playlist integration
├── contexts/
│   └── PlaylistContext.tsx       # State management
├── services/
│   └── playlistService.ts        # Database operations
├── types/
│   └── playlist.ts               # Type definitions
└── pages/
    └── Karaoke.tsx               # Updated with playlist tab

supabase/
└── migrations/
    └── 001_create_playlist_tables.sql  # Database schema
```

## Future Enhancements

1. **Drag & Drop Reordering**: Allow users to reorder videos in playlists
2. **Playlist Sharing**: Share playlists via URL
3. **Import/Export**: Import playlists from YouTube or export as JSON
4. **Collaborative Playlists**: Allow multiple users to contribute to playlists
5. **Smart Playlists**: Auto-generate playlists based on criteria
6. **Playlist Analytics**: Track play counts and popular videos
7. **Offline Support**: Cache playlists for offline viewing
8. **Playlist Templates**: Pre-made playlist templates for different occasions

## Technical Notes

- Uses Supabase for database operations
- Implements Row Level Security for data protection
- TypeScript for type safety
- React Context for state management
- Responsive design with Tailwind CSS
- Toast notifications for user feedback
- Error handling and loading states
