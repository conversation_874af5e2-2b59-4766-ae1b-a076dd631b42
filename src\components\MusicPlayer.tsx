
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Slider } from "@/components/ui/slider";

interface MusicPlayerProps {
  selectedSong: any;
}

/**
 * Komponen pemutar musik karaoke
 * - Kontrol play/pause/stop
 * - Progress bar lagu
 * - Volume control
 * - Display informasi lagu yang sedang diputar
 * - Animasi visual saat memutar musik
 */
const MusicPlayer = ({ selectedSong }: MusicPlayerProps) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [progress, setProgress] = useState(0);
  const [volume, setVolume] = useState([75]);
  const [currentTime, setCurrentTime] = useState("0:00");
  const [duration, setDuration] = useState("0:00");

  useEffect(() => {
    if (selectedSong) {
      setDuration(selectedSong.duration);
      setProgress(0);
      setCurrentTime("0:00");
    }
  }, [selectedSong]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isPlaying && selectedSong) {
      interval = setInterval(() => {
        setProgress((prev) => {
          const newProgress = prev + 1;
          // Simulasi progress berdasarkan durasi lagu
          const minutes = Math.floor(newProgress / 60);
          const seconds = newProgress % 60;
          setCurrentTime(`${minutes}:${seconds.toString().padStart(2, '0')}`);
          
          // Reset ketika lagu selesai (simulasi)
          if (newProgress >= 300) { // 5 menit simulasi
            setIsPlaying(false);
            return 0;
          }
          return newProgress;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isPlaying, selectedSong]);

  const handlePlayPause = () => {
    if (!selectedSong) return;
    setIsPlaying(!isPlaying);
  };

  const handleStop = () => {
    setIsPlaying(false);
    setProgress(0);
    setCurrentTime("0:00");
  };

  if (!selectedSong) {
    return (
      <div className="text-center py-8">
        <div className="text-6xl mb-4">🎵</div>
        <p className="text-white/60">Pilih lagu untuk memulai karaoke</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Song Info */}
      <div className="text-center">
        <div className={`text-4xl mb-4 ${isPlaying ? 'animate-bounce' : ''}`}>🎤</div>
        <h3 className="font-bold text-white text-lg">{selectedSong.title}</h3>
        <p className="text-white/70">{selectedSong.artist}</p>
      </div>

      {/* Visualizer Effect saat Playing */}
      {isPlaying && (
        <div className="flex justify-center space-x-1 py-4">
          {Array.from({ length: 5 }).map((_, i) => (
            <div
              key={i}
              className="w-1 bg-gradient-to-t from-pink-500 to-purple-500 rounded-full wave-animation"
              style={{
                height: `${Math.random() * 30 + 10}px`,
                animationDelay: `${i * 0.1}s`
              }}
            />
          ))}
        </div>
      )}

      {/* Progress Bar */}
      <div className="space-y-2">
        <Slider
          value={[progress]}
          onValueChange={(value) => setProgress(value[0])}
          max={300}
          step={1}
          className="w-full"
        />
        <div className="flex justify-between text-sm text-white/60">
          <span>{currentTime}</span>
          <span>{duration}</span>
        </div>
      </div>

      {/* Controls */}
      <div className="flex justify-center space-x-4">
        <Button
          size="lg"
          onClick={handleStop}
          className="bg-gray-600 hover:bg-gray-700"
        >
          ⏹️
        </Button>
        <Button
          size="lg"
          onClick={handlePlayPause}
          className="bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600 px-8"
        >
          {isPlaying ? '⏸️ Pause' : '▶️ Play'}
        </Button>
      </div>

      {/* Volume Control */}
      <div className="space-y-2">
        <label className="text-white/80 text-sm">Volume: {volume[0]}%</label>
        <Slider
          value={volume}
          onValueChange={setVolume}
          max={100}
          step={1}
          className="w-full"
        />
      </div>

      {/* Karaoke Mode Button */}
      <Button 
        className="w-full bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600"
        disabled={!isPlaying}
      >
        🎤 Mode Karaoke {!isPlaying && '(Play lagu dulu)'}
      </Button>
    </div>
  );
};

export default MusicPlayer;
