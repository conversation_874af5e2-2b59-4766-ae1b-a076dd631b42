
import { useState, useRef } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Upload, FileText } from "lucide-react";

interface LyricLine {
  time: number;
  text: string;
}

interface LyricsUploadProps {
  onLyricsUpload: (lyrics: LyricLine[]) => void;
  isDarkMode: boolean;
}

/**
 * Komponen untuk upload file lirik LRC atau TXT
 * - Support format LRC dengan timestamp
 * - Support format TXT biasa
 * - Parsing otomatis format file
 */
const LyricsUpload = ({ onLyricsUpload, isDarkMode }: LyricsUploadProps) => {
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const parseLRCFile = (content: string): LyricLine[] => {
    const lines = content.split('\n');
    const lyrics: LyricLine[] = [];

    lines.forEach(line => {
      // Format LRC: [mm:ss.xx]text atau [mm:ss]text
      const lrcMatch = line.match(/\[(\d{2}):(\d{2})(?:\.(\d{2}))?\](.*)/);
      if (lrcMatch) {
        const minutes = parseInt(lrcMatch[1]);
        const seconds = parseInt(lrcMatch[2]);
        const centiseconds = parseInt(lrcMatch[3] || '0');
        const text = lrcMatch[4].trim();
        
        const time = minutes * 60 + seconds + centiseconds / 100;
        if (text) {
          lyrics.push({ time, text });
        }
      }
    });

    return lyrics.sort((a, b) => a.time - b.time);
  };

  const parseTXTFile = (content: string): LyricLine[] => {
    const lines = content.split('\n').filter(line => line.trim());
    return lines.map((text, index) => ({
      time: index * 3, // 3 detik per baris sebagai default
      text: text.trim()
    }));
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setUploadedFile(file);
    const reader = new FileReader();
    
    reader.onload = (e) => {
      const content = e.target?.result as string;
      let lyrics: LyricLine[] = [];

      if (file.name.toLowerCase().endsWith('.lrc')) {
        lyrics = parseLRCFile(content);
      } else {
        lyrics = parseTXTFile(content);
      }

      onLyricsUpload(lyrics);
    };

    reader.readAsText(file);
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  return (
    <Card className={`${isDarkMode ? 'bg-white/10 border-white/20' : 'bg-black/10 border-black/20'} backdrop-blur-md`}>
      <CardHeader>
        <CardTitle className={`${isDarkMode ? 'text-white' : 'text-gray-900'} text-lg`}>
          Upload Lirik
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-center">
          <Button
            onClick={triggerFileInput}
            className="w-full bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600"
          >
            <Upload className="w-4 h-4 mr-2" />
            Pilih File Lirik
          </Button>
          <Input
            ref={fileInputRef}
            type="file"
            accept=".lrc,.txt"
            onChange={handleFileUpload}
            className="hidden"
          />
        </div>

        {uploadedFile && (
          <div className={`flex items-center space-x-2 p-3 rounded-lg ${isDarkMode ? 'bg-white/5' : 'bg-black/5'}`}>
            <FileText className={`w-4 h-4 ${isDarkMode ? 'text-white/70' : 'text-gray-700'}`} />
            <span className={`text-sm ${isDarkMode ? 'text-white/70' : 'text-gray-700'}`}>
              {uploadedFile.name}
            </span>
          </div>
        )}

        <div className={`text-xs ${isDarkMode ? 'text-white/50' : 'text-gray-500'} space-y-1`}>
          <p>• Format LRC: [00:12.34]Teks lirik</p>
          <p>• Format TXT: Teks lirik biasa</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default LyricsUpload;
