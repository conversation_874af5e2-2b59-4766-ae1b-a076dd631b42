-- =====================================================
-- YouTube Playlist Database Schema for MySQL/MariaDB
-- Compatible with XAMPP
-- =====================================================

-- Create database (optional - uncomment if needed)
-- CREATE DATABASE IF NOT EXISTS karaoke_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE karaoke_db;

-- =====================================================
-- Users Table (if not using external auth)
-- =====================================================
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(36) UNIQUE NOT NULL DEFAULT (UUID()),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    avatar_url TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    
    INDEX idx_users_uuid (uuid),
    INDEX idx_users_username (username),
    INDEX idx_users_email (email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- Playlists Table
-- =====================================================
CREATE TABLE IF NOT EXISTS playlists (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(36) UNIQUE NOT NULL DEFAULT (UUID()),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    user_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_public BOOLEAN DEFAULT FALSE,
    thumbnail_url TEXT,
    video_count INT DEFAULT 0,
    total_duration INT DEFAULT 0, -- in seconds
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_playlists_user_id (user_id),
    INDEX idx_playlists_is_public (is_public),
    INDEX idx_playlists_updated_at (updated_at DESC),
    INDEX idx_playlists_uuid (uuid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- Playlist Videos Table
-- =====================================================
CREATE TABLE IF NOT EXISTS playlist_videos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(36) UNIQUE NOT NULL DEFAULT (UUID()),
    playlist_id INT NOT NULL,
    video_id VARCHAR(255) NOT NULL,
    video_title TEXT NOT NULL,
    video_thumbnail TEXT NOT NULL,
    channel_title VARCHAR(255) NOT NULL,
    duration VARCHAR(50),
    duration_seconds INT DEFAULT 0,
    position INT NOT NULL DEFAULT 1,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (playlist_id) REFERENCES playlists(id) ON DELETE CASCADE,
    
    -- Prevent duplicate videos in the same playlist
    UNIQUE KEY unique_playlist_video (playlist_id, video_id),
    
    INDEX idx_playlist_videos_playlist_id (playlist_id),
    INDEX idx_playlist_videos_position (playlist_id, position),
    INDEX idx_playlist_videos_video_id (video_id),
    INDEX idx_playlist_videos_uuid (uuid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- Playlist Shares Table (for sharing playlists)
-- =====================================================
CREATE TABLE IF NOT EXISTS playlist_shares (
    id INT AUTO_INCREMENT PRIMARY KEY,
    playlist_id INT NOT NULL,
    share_token VARCHAR(64) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE,
    view_count INT DEFAULT 0,
    
    FOREIGN KEY (playlist_id) REFERENCES playlists(id) ON DELETE CASCADE,
    
    INDEX idx_playlist_shares_token (share_token),
    INDEX idx_playlist_shares_playlist_id (playlist_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- User Favorites Table (for favoriting playlists)
-- =====================================================
CREATE TABLE IF NOT EXISTS user_favorites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    playlist_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (playlist_id) REFERENCES playlists(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_user_favorite (user_id, playlist_id),
    
    INDEX idx_user_favorites_user_id (user_id),
    INDEX idx_user_favorites_playlist_id (playlist_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- Triggers for automatic updates
-- =====================================================

-- Trigger to update playlist video count and total duration
DELIMITER $$

CREATE TRIGGER update_playlist_stats_after_insert
AFTER INSERT ON playlist_videos
FOR EACH ROW
BEGIN
    UPDATE playlists 
    SET 
        video_count = (
            SELECT COUNT(*) 
            FROM playlist_videos 
            WHERE playlist_id = NEW.playlist_id
        ),
        total_duration = (
            SELECT COALESCE(SUM(duration_seconds), 0) 
            FROM playlist_videos 
            WHERE playlist_id = NEW.playlist_id
        ),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.playlist_id;
END$$

CREATE TRIGGER update_playlist_stats_after_delete
AFTER DELETE ON playlist_videos
FOR EACH ROW
BEGIN
    UPDATE playlists 
    SET 
        video_count = (
            SELECT COUNT(*) 
            FROM playlist_videos 
            WHERE playlist_id = OLD.playlist_id
        ),
        total_duration = (
            SELECT COALESCE(SUM(duration_seconds), 0) 
            FROM playlist_videos 
            WHERE playlist_id = OLD.playlist_id
        ),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = OLD.playlist_id;
END$$

CREATE TRIGGER update_playlist_stats_after_update
AFTER UPDATE ON playlist_videos
FOR EACH ROW
BEGIN
    UPDATE playlists 
    SET 
        video_count = (
            SELECT COUNT(*) 
            FROM playlist_videos 
            WHERE playlist_id = NEW.playlist_id
        ),
        total_duration = (
            SELECT COALESCE(SUM(duration_seconds), 0) 
            FROM playlist_videos 
            WHERE playlist_id = NEW.playlist_id
        ),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.playlist_id;
END$$

DELIMITER ;

-- =====================================================
-- Sample Data (Optional)
-- =====================================================

-- Insert sample user
INSERT INTO users (username, email, password_hash, full_name) VALUES 
('demo_user', '<EMAIL>', '$2y$10$example_hash', 'Demo User')
ON DUPLICATE KEY UPDATE username = username;

-- Get the user ID for sample data
SET @demo_user_id = (SELECT id FROM users WHERE username = 'demo_user' LIMIT 1);

-- Insert sample playlists
INSERT INTO playlists (name, description, user_id, is_public) VALUES 
('My Favorite Karaoke Songs', 'Collection of my favorite karaoke tracks', @demo_user_id, TRUE),
('Rock Classics', 'Classic rock songs for karaoke night', @demo_user_id, TRUE),
('Pop Hits', 'Popular songs everyone knows', @demo_user_id, FALSE)
ON DUPLICATE KEY UPDATE name = name;

-- =====================================================
-- Useful Views
-- =====================================================

-- View for playlist with user info
CREATE OR REPLACE VIEW playlist_with_user AS
SELECT 
    p.id,
    p.uuid,
    p.name,
    p.description,
    p.user_id,
    u.username,
    u.full_name,
    p.created_at,
    p.updated_at,
    p.is_public,
    p.thumbnail_url,
    p.video_count,
    p.total_duration
FROM playlists p
JOIN users u ON p.user_id = u.id;

-- View for playlist videos with playlist info
CREATE OR REPLACE VIEW playlist_videos_with_info AS
SELECT 
    pv.id,
    pv.uuid,
    pv.playlist_id,
    p.name as playlist_name,
    p.user_id,
    u.username,
    pv.video_id,
    pv.video_title,
    pv.video_thumbnail,
    pv.channel_title,
    pv.duration,
    pv.duration_seconds,
    pv.position,
    pv.added_at
FROM playlist_videos pv
JOIN playlists p ON pv.playlist_id = p.id
JOIN users u ON p.user_id = u.id;

-- =====================================================
-- Stored Procedures
-- =====================================================

DELIMITER $$

-- Procedure to reorder playlist videos
CREATE PROCEDURE ReorderPlaylistVideos(
    IN p_playlist_id INT,
    IN p_video_positions JSON
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_video_id VARCHAR(255);
    DECLARE v_new_position INT;
    DECLARE cur CURSOR FOR 
        SELECT JSON_UNQUOTE(JSON_EXTRACT(p_video_positions, CONCAT('$[', idx, '].video_id'))),
               JSON_UNQUOTE(JSON_EXTRACT(p_video_positions, CONCAT('$[', idx, '].position')))
        FROM (SELECT 0 as idx UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 
              UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) t
        WHERE JSON_EXTRACT(p_video_positions, CONCAT('$[', idx, ']')) IS NOT NULL;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    OPEN cur;
    read_loop: LOOP
        FETCH cur INTO v_video_id, v_new_position;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        UPDATE playlist_videos 
        SET position = v_new_position 
        WHERE playlist_id = p_playlist_id AND video_id = v_video_id;
    END LOOP;
    CLOSE cur;
END$$

-- Procedure to get user playlists with stats
CREATE PROCEDURE GetUserPlaylists(IN p_user_id INT)
BEGIN
    SELECT 
        p.*,
        u.username,
        u.full_name,
        (SELECT COUNT(*) FROM user_favorites uf WHERE uf.playlist_id = p.id) as favorite_count
    FROM playlists p
    JOIN users u ON p.user_id = u.id
    WHERE p.user_id = p_user_id OR p.is_public = TRUE
    ORDER BY p.updated_at DESC;
END$$

DELIMITER ;

-- =====================================================
-- Indexes for Performance
-- =====================================================

-- Additional composite indexes for common queries
CREATE INDEX idx_playlists_user_public ON playlists(user_id, is_public);
CREATE INDEX idx_playlist_videos_playlist_position ON playlist_videos(playlist_id, position);
CREATE INDEX idx_users_active ON users(is_active);

-- =====================================================
-- End of Schema
-- =====================================================

-- Show tables created
SHOW TABLES LIKE '%playlist%';
SHOW TABLES LIKE 'users';

-- Show table structures
DESCRIBE playlists;
DESCRIBE playlist_videos;
