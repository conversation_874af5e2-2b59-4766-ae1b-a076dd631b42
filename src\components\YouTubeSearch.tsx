import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { supabase } from "@/integrations/supabase/client";
import { Search, Play, Plus } from "lucide-react";
import AddToPlaylistDialog from "./AddToPlaylistDialog";

interface YouTubeVideo {
  id: string;
  title: string;
  thumbnail: string;
  channelTitle: string;
  publishedAt: string;
  description: string;
}

interface YouTubeSearchProps {
  onVideoSelect: (video: YouTubeVideo) => void;
}

/**
 * Komponen pencarian video karaoke YouTube
 * - Pencarian video karaoke menggunakan YouTube Data API
 * - Filter otomatis untuk video instrumental/minus one
 * - Tampilan grid dengan thumbnail dan informasi video
 */
const YouTubeSearch = ({ onVideoSelect }: YouTubeSearchProps) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [videos, setVideos] = useState<YouTubeVideo[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [selectedVideoForPlaylist, setSelectedVideoForPlaylist] = useState<YouTubeVideo | null>(null);
  const [isAddToPlaylistOpen, setIsAddToPlaylistOpen] = useState(false);

  const searchVideos = async () => {
    if (!searchQuery.trim()) return;

    setLoading(true);
    setError("");

    try {
      const { data, error } = await supabase.functions.invoke('youtube-search', {
        body: { query: searchQuery }
      });

      if (error) throw error;

      setVideos(data.videos || []);
      
      if (data.videos?.length === 0) {
        setError("Tidak ada video karaoke yang ditemukan. Coba kata kunci lain.");
      }
    } catch (err: any) {
      console.error('Error searching videos:', err);
      setError("Gagal mencari video. Silakan coba lagi.");
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      searchVideos();
    }
  };

  const handlePlayVideo = (video: YouTubeVideo) => {
    // Navigasi ke halaman player dengan parameter video
    const playerUrl = `/player?video=${video.id}&title=${encodeURIComponent(video.title)}`;
    window.open(playerUrl, '_blank');
  };

  const handleAddToPlaylist = (video: YouTubeVideo) => {
    setSelectedVideoForPlaylist(video);
    setIsAddToPlaylistOpen(true);
  };

  const handleCloseAddToPlaylist = () => {
    setIsAddToPlaylistOpen(false);
    setSelectedVideoForPlaylist(null);
  };

  return (
    <div className="space-y-6">
      {/* Search Input */}
      <div className="flex space-x-2">
        <Input
          placeholder="Masukkan judul lagu untuk mencari karaoke..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onKeyPress={handleKeyPress}
          className="bg-white/20 border-white/30 text-white placeholder:text-white/60"
        />
        <Button 
          onClick={searchVideos}
          disabled={loading || !searchQuery.trim()}
          className="bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600"
        >
          <Search className="w-4 h-4 mr-2" />
          {loading ? "Mencari..." : "Cari"}
        </Button>
      </div>

      {/* Error Message */}
      {error && (
        <div className="text-center py-4">
          <p className="text-red-400">{error}</p>
        </div>
      )}

      {/* Videos Grid */}
      {videos.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
          {videos.map((video) => (
            <Card 
              key={video.id}
              className="bg-white/10 backdrop-blur-md border-white/20 hover:bg-white/20 transition-all duration-300 cursor-pointer"
              onClick={() => onVideoSelect(video)}
            >
              <CardContent className="p-3">
                <div className="relative mb-3">
                  <img 
                    src={video.thumbnail} 
                    alt={video.title}
                    className="w-full h-32 object-cover rounded-lg"
                  />
                  <div className="absolute inset-0 flex items-center justify-center bg-black/30 rounded-lg opacity-0 hover:opacity-100 transition-opacity">
                    <Play className="w-8 h-8 text-white" />
                  </div>
                </div>
                <h3 className="text-white font-semibold text-sm line-clamp-2 mb-2">
                  {video.title}
                </h3>
                <p className="text-white/70 text-xs mb-2">{video.channelTitle}</p>
                <div className="space-y-2">
                  <Button
                    size="sm"
                    className="w-full bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600"
                    onClick={(e) => {
                      e.stopPropagation();
                      onVideoSelect(video);
                    }}
                  >
                    <Play className="w-3 h-3 mr-1" />
                    Putar di Tab Ini
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    className="w-full bg-white/10 border-white/20 text-white hover:bg-white/20"
                    onClick={(e) => {
                      e.stopPropagation();
                      handlePlayVideo(video);
                    }}
                  >
                    🎤 Buka Player Karaoke
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    className="w-full bg-white/10 border-white/20 text-white hover:bg-white/20"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAddToPlaylist(video);
                    }}
                  >
                    <Plus className="w-3 h-3 mr-1" />
                    Tambah ke Playlist
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* No results message */}
      {!loading && videos.length === 0 && searchQuery && !error && (
        <div className="text-center py-8">
          <p className="text-white/60">Mulai mencari dengan memasukkan judul lagu</p>
        </div>
      )}

      {/* Add to Playlist Dialog */}
      <AddToPlaylistDialog
        video={selectedVideoForPlaylist}
        isOpen={isAddToPlaylistOpen}
        onClose={handleCloseAddToPlaylist}
      />
    </div>
  );
};

export default YouTubeSearch;
