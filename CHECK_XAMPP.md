# 🔍 XAMPP Troubleshooting Checklist

## Step 1: Check XAMPP Control Panel
1. Buka XAMPP Control Panel
2. Pastikan status:
   - ✅ **Apache**: Running (hijau) - Port 80, 443
   - ✅ **MySQL**: Running (hijau) - Port 3306

## Step 2: Test Basic Apache
Buka browser dan akses: `http://localhost`
- ✅ **Expected**: XAMPP Dashboard muncul
- ❌ **If failed**: Apache tidak running atau port bentrok

## Step 3: Check File Structure
Pastikan struktur folder seperti ini:
```
C:\xampp\htdocs\
└── sing-along-web-beats\
    ├── api\
    │   ├── config\
    │   │   └── database.php
    │   ├── playlists.php
    │   ├── debug.php
    │   └── .htaccess
    ├── src\
    └── package.json
```

## Step 4: Test API Files Manually
Buka browser dan test URL ini satu per satu:

1. **Test 1**: `http://localhost/sing-along-web-beats/`
   - Expected: Folder listing atau React app

2. **Test 2**: `http://localhost/sing-along-web-beats/api/`
   - Expected: Folder listing atau error message

3. **Test 3**: `http://localhost/sing-along-web-beats/api/debug.php`
   - Expected: JSON response

## Common Issues & Quick Fixes

### Issue 1: Apache Won't Start (Port 80 Busy)
**Symptoms**: Apache button stays red
**Solution**:
1. Stop Skype/IIS/other services using port 80
2. Or change Apache port:
   - Click "Config" → "Apache (httpd.conf)"
   - Find `Listen 80` → change to `Listen 8080`
   - Restart Apache
   - Access via `http://localhost:8080`

### Issue 2: Files Not Found
**Symptoms**: 404 Not Found
**Solution**:
1. Check if files exist in correct location
2. Copy API files to correct folder
3. Check file permissions

### Issue 3: Permission Denied
**Symptoms**: 403 Forbidden
**Solution**:
1. Run XAMPP as Administrator
2. Check folder permissions
3. Disable antivirus temporarily
