<?php
/**
 * Database Configuration for XAMPP MySQL
 * Karaoke Playlist Application
 */

class Database {
    // Database configuration
    private $host = "localhost";
    private $db_name = "karaoke_db";
    private $username = "root";
    private $password = "";
    private $charset = "utf8mb4";
    
    public $conn;
    
    /**
     * Get database connection
     */
    public function getConnection() {
        $this->conn = null;
        
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            
            $this->conn = new PDO($dsn, $this->username, $this->password, $options);
        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        }
        
        return $this->conn;
    }
    
    /**
     * Close database connection
     */
    public function closeConnection() {
        $this->conn = null;
    }
    
    /**
     * Test database connection
     */
    public function testConnection() {
        try {
            $conn = $this->getConnection();
            if ($conn) {
                return [
                    'status' => 'success',
                    'message' => 'Database connection successful',
                    'server_info' => $conn->getAttribute(PDO::ATTR_SERVER_INFO)
                ];
            }
        } catch (Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Database connection failed: ' . $e->getMessage()
            ];
        }
    }
}

/**
 * CORS Headers for API
 */
function setCorsHeaders() {
    // Allow from any origin
    header("Access-Control-Allow-Origin: *");
    header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
    header("Access-Control-Allow-Headers: Content-Type, Authorization, X-User-ID, X-Requested-With");
    header("Access-Control-Max-Age: 86400");

    // Handle preflight OPTIONS requests
    if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
        http_response_code(200);
        exit(0);
    }
}

/**
 * JSON Response Helper
 */
function jsonResponse($data, $status_code = 200) {
    http_response_code($status_code);
    header('Content-Type: application/json');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * Error Response Helper
 */
function errorResponse($message, $status_code = 400, $details = null) {
    $response = [
        'error' => true,
        'message' => $message
    ];
    
    if ($details) {
        $response['details'] = $details;
    }
    
    jsonResponse($response, $status_code);
}

/**
 * Success Response Helper
 */
function successResponse($data = null, $message = 'Success') {
    $response = [
        'error' => false,
        'message' => $message
    ];
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    jsonResponse($response);
}

/**
 * Validate Required Fields
 */
function validateRequired($data, $required_fields) {
    $missing = [];
    
    foreach ($required_fields as $field) {
        if (!isset($data[$field]) || empty(trim($data[$field]))) {
            $missing[] = $field;
        }
    }
    
    if (!empty($missing)) {
        errorResponse('Missing required fields: ' . implode(', ', $missing), 400);
    }
}

/**
 * Sanitize Input
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

/**
 * Generate UUID v4
 */
function generateUUID() {
    return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
        mt_rand(0, 0xffff), mt_rand(0, 0xffff),
        mt_rand(0, 0xffff),
        mt_rand(0, 0x0fff) | 0x4000,
        mt_rand(0, 0x3fff) | 0x8000,
        mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
    );
}

/**
 * Parse YouTube Duration (PT4M13S format) to seconds
 */
function parseYouTubeDuration($duration) {
    if (empty($duration)) return 0;
    
    $interval = new DateInterval($duration);
    return ($interval->h * 3600) + ($interval->i * 60) + $interval->s;
}

/**
 * Format seconds to duration string (MM:SS or HH:MM:SS)
 */
function formatDuration($seconds) {
    if ($seconds < 3600) {
        return sprintf('%02d:%02d', floor($seconds / 60), $seconds % 60);
    } else {
        return sprintf('%02d:%02d:%02d', floor($seconds / 3600), floor(($seconds % 3600) / 60), $seconds % 60);
    }
}

/**
 * Simple Authentication (you can replace with JWT or session-based auth)
 */
function getCurrentUserId() {
    // For demo purposes, return a default user ID
    // In production, implement proper authentication
    $headers = getallheaders();
    
    if (isset($headers['X-User-ID'])) {
        return (int)$headers['X-User-ID'];
    }
    
    // Default demo user
    return 1;
}

/**
 * Check if user owns playlist
 */
function checkPlaylistOwnership($playlist_id, $user_id, $db) {
    $query = "SELECT user_id FROM playlists WHERE id = :playlist_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':playlist_id', $playlist_id);
    $stmt->execute();
    
    $result = $stmt->fetch();
    
    if (!$result) {
        errorResponse('Playlist not found', 404);
    }
    
    if ($result['user_id'] != $user_id) {
        errorResponse('Access denied', 403);
    }
    
    return true;
}

/**
 * Log API requests (optional)
 */
function logRequest($endpoint, $method, $user_id = null) {
    $log_entry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'endpoint' => $endpoint,
        'method' => $method,
        'user_id' => $user_id,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
    ];
    
    // You can save this to a log file or database
    error_log(json_encode($log_entry), 3, 'api_logs.txt');
}
?>
