<?php
/**
 * CORS Test Endpoint
 * Test if CORS is working properly
 */

// Set CORS headers
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-User-ID, X-Requested-With");
header("Access-Control-Max-Age: 86400");
header("Content-Type: application/json");

// Handle preflight OPTIONS requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    echo json_encode([
        'status' => 'success',
        'message' => 'CORS preflight OK',
        'method' => 'OPTIONS'
    ]);
    exit(0);
}

// Handle other requests
$response = [
    'status' => 'success',
    'message' => 'CORS test successful',
    'method' => $_SERVER['REQUEST_METHOD'],
    'timestamp' => date('Y-m-d H:i:s'),
    'headers' => [
        'Access-Control-Allow-Origin' => '*',
        'Access-Control-Allow-Methods' => 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers' => 'Content-Type, Authorization, X-User-ID, X-Requested-With'
    ],
    'request_info' => [
        'origin' => $_SERVER['HTTP_ORIGIN'] ?? 'not set',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'not set',
        'content_type' => $_SERVER['CONTENT_TYPE'] ?? 'not set'
    ]
];

http_response_code(200);
echo json_encode($response, JSON_PRETTY_PRINT);
?>
