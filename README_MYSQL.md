# Setup Database MySQL untuk Fitur Playlist YouTube

## 📋 <PERSON>kasan
<PERSON> lengkap untuk setup database MySQL menggunakan XAMPP untuk fitur playlist YouTube pada aplikasi karaoke.

## 🚀 Quick Start

### 1. Setup XAMPP
1. Download dan install XAMPP dari [https://www.apachefriends.org/](https://www.apachefriends.org/)
2. Start **Apache** dan **MySQL** dari XAMPP Control Panel
3. Buka browser dan akses `http://localhost/phpmyadmin`

### 2. Buat Database
```sql
CREATE DATABASE karaoke_app CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 3. Import Schema
1. Pilih database `karaoke_app`
2. Klik tab "Import"
3. Upload file `database/mysql_playlist_schema.sql`
4. Klik "Go"

### 4. Copy API Files
Copy folder `api/` ke dalam `htdocs/sing-along-web-beats/`

### 5. Test Setup
Akses: `http://localhost/sing-along-web-beats/api/test-connection.php`

## 📁 Struktur File

```
htdocs/sing-along-web-beats/
├── api/
│   ├── config/
│   │   └── database.php          # Konfigurasi database
│   ├── playlists.php             # API playlist management
│   ├── playlist-videos.php       # API video management
│   ├── test-connection.php       # Test koneksi database
│   └── .htaccess                 # URL routing
├── database/
│   └── mysql_playlist_schema.sql # Schema database
├── src/
│   └── services/
│       └── playlistService.ts    # Updated untuk MySQL API
└── XAMPP_SETUP.md               # Panduan detail
```

## 🗄️ Schema Database

### Tables Created:
- **users** - Data pengguna
- **playlists** - Data playlist
- **playlist_videos** - Video dalam playlist
- **playlist_shares** - Sharing playlist
- **user_favorites** - Playlist favorit

### Sample Data:
- Demo user: `demo_user` (ID: 1)
- 3 sample playlists
- Triggers untuk auto-update stats

## 🔌 API Endpoints

### Base URL
```
http://localhost/sing-along-web-beats/api/
```

### Playlist Management
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/playlists` | Get all playlists |
| POST | `/playlists` | Create new playlist |
| PUT | `/playlists` | Update playlist |
| DELETE | `/playlists?id={id}` | Delete playlist |

### Video Management
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/playlist-videos?playlist_id={id}` | Get videos in playlist |
| POST | `/playlist-videos` | Add video to playlist |
| PUT | `/playlist-videos` | Reorder videos |
| DELETE | `/playlist-videos?playlist_id={id}&video_id={video_id}` | Remove video |

## 🧪 Testing API

### Test Connection
```bash
curl http://localhost/sing-along-web-beats/api/test-connection.php
```

### Create Playlist
```bash
curl -X POST http://localhost/sing-along-web-beats/api/playlists \
  -H "Content-Type: application/json" \
  -H "X-User-ID: 1" \
  -d '{
    "name": "My Test Playlist",
    "description": "Testing playlist creation",
    "is_public": false
  }'
```

### Add Video to Playlist
```bash
curl -X POST http://localhost/sing-along-web-beats/api/playlist-videos \
  -H "Content-Type: application/json" \
  -H "X-User-ID: 1" \
  -d '{
    "playlist_id": 1,
    "video": {
      "id": "dQw4w9WgXcQ",
      "title": "Rick Astley - Never Gonna Give You Up (Karaoke)",
      "thumbnail": "https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg",
      "channelTitle": "Rick Astley",
      "duration": "PT3M33S"
    }
  }'
```

## ⚙️ Konfigurasi

### Database Config (`api/config/database.php`)
```php
private $host = "localhost";
private $db_name = "karaoke_app";
private $username = "root";
private $password = ""; // Default XAMPP
```

### Frontend Config (`src/services/playlistService.ts`)
```typescript
const API_BASE_URL = 'http://localhost/sing-along-web-beats/api';
const DEFAULT_USER_ID = '1'; // Demo user
```

## 🔧 Troubleshooting

### ❌ Database connection failed
**Solusi:**
- Pastikan MySQL service running di XAMPP
- Check username/password di `database.php`
- Pastikan database `karaoke_app` exists

### ❌ Table doesn't exist
**Solusi:**
- Re-import `mysql_playlist_schema.sql`
- Check semua tables terbuat dengan benar

### ❌ CORS Error
**Solusi:**
- Pastikan file `.htaccess` ada di folder `api/`
- Enable mod_rewrite di Apache XAMPP

### ❌ 404 Not Found
**Solusi:**
- Check struktur folder sudah benar
- Pastikan API files ada di `htdocs/sing-along-web-beats/api/`

### ❌ Permission Denied
**Solusi:**
- Set permission folder `htdocs` agar writable
- Run XAMPP as administrator (Windows)

## 🔒 Security Notes

⚠️ **Development Only**
- Authentication menggunakan simple header `X-User-ID`
- Untuk production, implementasikan proper JWT/session auth
- Gunakan HTTPS untuk production
- Validate semua input data

## 📝 Next Steps

1. ✅ Setup database dan API
2. ✅ Test semua endpoints
3. 🔄 Update frontend untuk menggunakan MySQL API
4. 🔄 Implementasi proper authentication
5. 🔄 Add error handling yang lebih baik
6. 🔄 Optimize database queries
7. 🔄 Add caching layer

## 📞 Support

Jika ada masalah:
1. Check XAMPP Control Panel - pastikan Apache & MySQL running
2. Check browser console untuk error messages
3. Check XAMPP error logs di `xampp/apache/logs/error.log`
4. Test API endpoints menggunakan Postman atau curl

## 🎯 Features Ready

✅ **Implemented:**
- Create, read, update, delete playlists
- Add/remove videos from playlists
- Reorder videos in playlists
- User-specific playlists
- Public/private playlist settings
- Auto-update playlist statistics
- CORS support for frontend integration

🔄 **Next Features:**
- Playlist sharing via tokens
- Favorite playlists
- Search within playlists
- Bulk operations
- Playlist templates
- Export/import playlists
