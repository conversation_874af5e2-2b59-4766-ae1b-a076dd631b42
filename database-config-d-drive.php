<?php
/**
 * Database Configuration for XAMPP MySQL (D: Drive)
 * Copy this to: D:\XAMPP\htdocs\sing-along-web-beats\api\config\database.php
 */

class Database {
    // Database configuration for D: drive XAMPP
    private $host = "localhost";
    private $db_name = "karaoke_db";
    private $username = "root";
    private $password = "";
    private $charset = "utf8mb4";
    
    public $conn;
    
    /**
     * Get database connection
     */
    public function getConnection() {
        $this->conn = null;
        
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            
            $this->conn = new PDO($dsn, $this->username, $this->password, $options);
        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        }
        
        return $this->conn;
    }
    
    /**
     * Test database connection
     */
    public function testConnection() {
        try {
            $conn = $this->getConnection();
            if ($conn) {
                return [
                    'status' => 'success',
                    'message' => 'Database connection successful (D: Drive XAMPP)',
                    'server_info' => $conn->getAttribute(PDO::ATTR_SERVER_INFO),
                    'xampp_location' => 'D:\XAMPP'
                ];
            }
        } catch (Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Database connection failed: ' . $e->getMessage(),
                'xampp_location' => 'D:\XAMPP'
            ];
        }
    }
}

/**
 * CORS Headers for API
 */
function setCorsHeaders() {
    header("Access-Control-Allow-Origin: *");
    header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
    header("Access-Control-Allow-Headers: Content-Type, Authorization, X-User-ID, X-Requested-With");
    header("Access-Control-Max-Age: 86400");
    
    if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
        http_response_code(200);
        exit(0);
    }
}

/**
 * JSON Response Helper
 */
function jsonResponse($data, $status_code = 200) {
    http_response_code($status_code);
    header('Content-Type: application/json');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * Error Response Helper
 */
function errorResponse($message, $status_code = 400, $details = null) {
    $response = [
        'error' => true,
        'message' => $message,
        'xampp_location' => 'D:\XAMPP'
    ];
    
    if ($details) {
        $response['details'] = $details;
    }
    
    jsonResponse($response, $status_code);
}

/**
 * Success Response Helper
 */
function successResponse($data = null, $message = 'Success') {
    $response = [
        'error' => false,
        'message' => $message,
        'xampp_location' => 'D:\XAMPP'
    ];
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    jsonResponse($response);
}

// Other helper functions...
function validateRequired($data, $required_fields) {
    $missing = [];
    foreach ($required_fields as $field) {
        if (!isset($data[$field]) || empty(trim($data[$field]))) {
            $missing[] = $field;
        }
    }
    if (!empty($missing)) {
        errorResponse('Missing required fields: ' . implode(', ', $missing), 400);
    }
}

function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

function getCurrentUserId() {
    $headers = getallheaders();
    if (isset($headers['X-User-ID'])) {
        return (int)$headers['X-User-ID'];
    }
    return 1; // Default demo user
}
?>
