<?php
/**
 * Simple Apache Test File
 * Place this in htdocs/ to test if Apache is working
 */

// Set headers
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json");

// Basic info
$test_info = [
    'status' => 'Apache is working!',
    'timestamp' => date('Y-m-d H:i:s'),
    'php_version' => phpversion(),
    'server_info' => [
        'server_name' => $_SERVER['SERVER_NAME'] ?? 'unknown',
        'server_port' => $_SERVER['SERVER_PORT'] ?? 'unknown',
        'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'unknown',
        'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown'
    ],
    'file_location' => __FILE__,
    'instructions' => [
        '1. If you see this, Apache is working',
        '2. Next, check if your API files exist',
        '3. Copy API files to htdocs/sing-along-web-beats/api/',
        '4. Test: http://localhost/sing-along-web-beats/api/debug.php'
    ]
];

echo json_encode($test_info, JSON_PRETTY_PRINT);
?>
