
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import SongList from "@/components/SongList";
import MusicPlayer from "@/components/MusicPlayer";
import YouTubeSearch from "@/components/YouTubeSearch";
import YouTubePlayer from "@/components/YouTubePlayer";
import PlaylistManager from "@/components/PlaylistManager";
import PlaylistViewer from "@/components/PlaylistViewer";
import type { Playlist, PlaylistVideo } from "@/types/playlist";

/**
 * Halaman karaoke utama
 * - Daftar lagu yang tersedia (contoh)
 * - Pencarian video karaoke YouTube
 * - Player musik dengan kontrol
 * - Interface yang responsif dan interaktif
 */
const Karaoke = () => {
  const [selectedSong, setSelectedSong] = useState<any>(null);
  const [selectedVideo, setSelectedVideo] = useState<any>(null);
  const [searchQ<PERSON>y, setSearchQuery] = useState("");
  const [selectedPlaylist, setSelectedPlaylist] = useState<Playlist | null>(null);
  const [viewMode, setViewMode] = useState<'list' | 'viewer'>('list');

  const handleVideoSelect = (video: any) => {
    setSelectedVideo(video);
    // Reset selected song when playing YouTube video
    setSelectedSong(null);
  };

  const handleSongSelect = (song: any) => {
    setSelectedSong(song);
    // Reset selected video when playing local song
    setSelectedVideo(null);
  };

  const handlePlaylistSelect = (playlist: Playlist) => {
    setSelectedPlaylist(playlist);
    setViewMode('viewer');
  };

  const handleBackToPlaylists = () => {
    setViewMode('list');
    setSelectedPlaylist(null);
  };

  const handlePlaylistVideoSelect = (video: PlaylistVideo) => {
    // Convert PlaylistVideo to YouTubeVideo format
    const youtubeVideo = {
      id: video.video_id,
      title: video.video_title,
      thumbnail: video.video_thumbnail,
      channelTitle: video.channel_title,
      publishedAt: video.added_at,
      description: '',
      duration: video.duration
    };
    handleVideoSelect(youtubeVideo);
  };

  return (
    <div className="min-h-screen py-8 px-4">
      <div className="container mx-auto max-w-6xl">
        <div className="text-center mb-8">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4 bg-gradient-to-r from-pink-400 to-purple-400 bg-clip-text text-transparent">
            🎤 Karaoke Time!
          </h1>
          <p className="text-white/80 text-lg">Pilih lagu favorit Anda dan mulai bernyanyi</p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Song Selection Section */}
          <div className="lg:col-span-2">
            <Card className="bg-white/10 backdrop-blur-md border-white/20">
              <CardHeader>
                <CardTitle className="text-white text-xl">Pilih Lagu Karaoke</CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="youtube" className="w-full">
                  <TabsList className="grid w-full grid-cols-3 bg-white/10">
                    <TabsTrigger value="youtube" className="text-white data-[state=active]:bg-gradient-to-r data-[state=active]:from-pink-500 data-[state=active]:to-purple-500">
                      YouTube Karaoke
                    </TabsTrigger>
                    <TabsTrigger value="playlists" className="text-white data-[state=active]:bg-gradient-to-r data-[state=active]:from-pink-500 data-[state=active]:to-purple-500">
                      Playlist
                    </TabsTrigger>
                    <TabsTrigger value="local" className="text-white data-[state=active]:bg-gradient-to-r data-[state=active]:from-pink-500 data-[state=active]:to-purple-500">
                      Daftar Lagu
                    </TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="youtube" className="mt-4">
                    <YouTubeSearch onVideoSelect={handleVideoSelect} />
                  </TabsContent>

                  <TabsContent value="playlists" className="mt-4">
                    {viewMode === 'list' ? (
                      <PlaylistManager onPlaylistSelect={handlePlaylistSelect} />
                    ) : selectedPlaylist ? (
                      <PlaylistViewer
                        playlist={selectedPlaylist}
                        onBack={handleBackToPlaylists}
                        onVideoSelect={handlePlaylistVideoSelect}
                      />
                    ) : null}
                  </TabsContent>

                  <TabsContent value="local" className="mt-4">
                    <SongList
                      searchQuery={searchQuery}
                      onSongSelect={handleSongSelect}
                      selectedSong={selectedSong}
                    />
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>

          {/* Player Section */}
          <div className="lg:col-span-1">
            <div className="space-y-4 sticky top-8">
              {/* YouTube Player */}
              {selectedVideo && <YouTubePlayer selectedVideo={selectedVideo} />}
              
              {/* Local Music Player */}
              {selectedSong && !selectedVideo && (
                <Card className="bg-white/10 backdrop-blur-md border-white/20">
                  <CardHeader>
                    <CardTitle className="text-white text-xl">Player</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <MusicPlayer selectedSong={selectedSong} />
                  </CardContent>
                </Card>
              )}

              {/* Default state when nothing is selected */}
              {!selectedVideo && !selectedSong && (
                <Card className="bg-white/10 backdrop-blur-md border-white/20">
                  <CardHeader>
                    <CardTitle className="text-white text-xl">Player</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-12">
                      <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full flex items-center justify-center">
                        🎵
                      </div>
                      <p className="text-white/60">Pilih lagu untuk mulai karaoke</p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Karaoke;
