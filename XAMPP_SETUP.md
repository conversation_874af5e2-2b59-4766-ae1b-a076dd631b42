# Setup Database untuk XAMPP

## Langkah-langkah Setup

### 1. Persiapan XAMPP
1. Pastikan XAMPP sudah terinstall
2. Start Apache dan MySQL dari XAMPP Control Panel
3. Buka phpMyAdmin di browser: `http://localhost/phpmyadmin`

### 2. Membuat Database
1. Di phpMyAdmin, klik "New" untuk membuat database baru
2. Nama database: `karaoke_db`
3. Collation: `utf8mb4_unicode_ci`
4. Klik "Create"

### 3. Import Schema Database
1. Pilih database `karaoke_db` yang baru dibuat
2. Klik tab "Import"
3. Pilih file `database/mysql_playlist_schema.sql`
4. Klik "Go" untuk mengeksekusi

### 4. Setup API Files
1. Copy folder `api/` ke dalam folder `htdocs/sing-along-web-beats/`
2. Pastikan struktur folder seperti ini:
   ```
   htdocs/
   └── sing-along-web-beats/
       ├── api/
       │   ├── config/
       │   │   └── database.php
       │   ├── playlists.php
       │   ├── playlist-videos.php
       │   ├── test-connection.php
       │   └── .htaccess
       └── (file React lainnya)
   ```

### 5. Konfigurasi Database
Edit file `api/config/database.php` jika perlu:
```php
private $host = "localhost";
private $db_name = "karaoke_db";
private $username = "root";
private $password = ""; // Kosong untuk XAMPP default
```

### 6. Test Koneksi
1. Buka browser dan akses: `http://localhost/sing-along-web-beats/api/test-connection.php`
2. Jika berhasil, akan muncul response JSON dengan status "success"

## API Endpoints

### Base URL
```
http://localhost/sing-along-web-beats/api/
```

### Playlist Management
- **GET** `/playlists` - Get all playlists
- **POST** `/playlists` - Create new playlist
- **PUT** `/playlists` - Update playlist
- **DELETE** `/playlists?id={id}` - Delete playlist

### Playlist Videos
- **GET** `/playlist-videos?playlist_id={id}` - Get videos in playlist
- **POST** `/playlist-videos` - Add video to playlist
- **PUT** `/playlist-videos` - Reorder videos
- **DELETE** `/playlist-videos?playlist_id={id}&video_id={video_id}` - Remove video

## Contoh Request

### Membuat Playlist Baru
```javascript
fetch('http://localhost/sing-along-web-beats/api/playlists', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-User-ID': '1' // ID user (untuk demo)
  },
  body: JSON.stringify({
    name: 'My Karaoke Playlist',
    description: 'Lagu-lagu favorit untuk karaoke',
    is_public: false
  })
})
```

### Menambah Video ke Playlist
```javascript
fetch('http://localhost/sing-along-web-beats/api/playlist-videos', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-User-ID': '1'
  },
  body: JSON.stringify({
    playlist_id: 1,
    video: {
      id: 'dQw4w9WgXcQ',
      title: 'Rick Astley - Never Gonna Give You Up (Karaoke)',
      thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg',
      channelTitle: 'Rick Astley',
      duration: 'PT3M33S'
    }
  })
})
```

## Update Frontend untuk Menggunakan API

Update file `src/services/playlistService.ts`:

```typescript
const API_BASE_URL = 'http://localhost/sing-along-web-beats/api';

export class PlaylistService {
  static async createPlaylist(data: CreatePlaylistData): Promise<Playlist> {
    const response = await fetch(`${API_BASE_URL}/playlists`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-User-ID': '1' // Ganti dengan user ID yang sebenarnya
      },
      body: JSON.stringify(data)
    });
    
    const result = await response.json();
    if (!response.ok) {
      throw new Error(result.message || 'Failed to create playlist');
    }
    
    return result.data;
  }
  
  // ... method lainnya
}
```

## Troubleshooting

### Error: Database connection failed
- Pastikan MySQL service di XAMPP sudah running
- Check username/password di `database.php`
- Pastikan database `karaoke_db` sudah dibuat

### Error: Table doesn't exist
- Import ulang file `mysql_playlist_schema.sql`
- Pastikan semua table sudah terbuat dengan benar

### Error: CORS
- Pastikan file `.htaccess` ada di folder `api/`
- Check apakah mod_rewrite enabled di Apache

### Error: 404 Not Found
- Pastikan struktur folder sudah benar
- Check apakah file API ada di lokasi yang tepat

## Sample Data

Setelah import schema, akan ada sample data:
- User: `demo_user` (ID: 1)
- 3 sample playlists
- Gunakan user ID 1 untuk testing

## Security Notes

⚠️ **Untuk Development Only**
- Authentication sangat sederhana (hanya X-User-ID header)
- Untuk production, implementasikan proper authentication
- Gunakan HTTPS untuk production
- Validate dan sanitize semua input

## Next Steps

1. Test semua API endpoints
2. Update frontend untuk menggunakan API
3. Implementasi proper authentication
4. Add error handling yang lebih baik
5. Optimize database queries
