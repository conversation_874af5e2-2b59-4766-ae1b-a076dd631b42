# 🔧 Fix "Failed to fetch" Error

## ❌ Error yang <PERSON>
```
Error: Failed to fetch saat di tekan playlist
```

## 🔍 Diagnosis Steps

### 1. Test API Endpoints Manual
Buka browser dan test URL berikut satu per satu:

```
http://localhost/sing-along-web-beats/api/debug
http://localhost/sing-along-web-beats/api/test-cors
http://localhost/sing-along-web-beats/api/test-connection
http://localhost/sing-along-web-beats/api/playlists
```

### 2. Check XAMPP Status
- ✅ Apache: Running (hijau)
- ✅ MySQL: Running (hijau)
- ❌ <PERSON><PERSON> merah, klik "Start"

### 3. Check File Structure
Pastikan struktur folder seperti ini:
```
htdocs/
└── sing-along-web-beats/
    ├── api/
    │   ├── config/
    │   │   └── database.php
    │   ├── playlists.php
    │   ├── playlist-videos.php
    │   ├── debug.php
    │   ├── test-cors.php
    │   └── .htaccess
    └── (React files)
```

## 🚀 Quick Fix Steps

### Step 1: Restart XAMPP
1. Stop Apache & MySQL
2. Wait 5 seconds
3. Start Apache & MySQL
4. Check status (harus hijau)

### Step 2: Test API Manual
Buka: `http://localhost/sing-along-web-beats/api/debug`

**Expected Result:**
```json
{
  "status": "API is working",
  "database": "Connected successfully",
  "tables": {
    "users": "exists (rows: 1)",
    "playlists": "exists (rows: 3)",
    "playlist_videos": "exists (rows: 0)"
  }
}
```

### Step 3: Test dari React App
1. Buka browser console (F12)
2. Go to Karaoke page
3. Click Playlist tab
4. Check console untuk error messages

### Step 4: Use Debug Button
Di Playlist tab, jika ada error, akan muncul tombol "Test API Connection". Klik untuk test.

## 🔧 Common Issues & Solutions

### Issue 1: Port 80 Bentrok
**Symptoms:** Apache tidak bisa start
**Solution:**
1. Buka XAMPP Control Panel
2. Klik "Config" → "Apache (httpd.conf)"
3. Cari `Listen 80` dan ganti ke `Listen 8080`
4. Restart Apache
5. Update API URL di `src/services/playlistService.ts`:
   ```typescript
   const API_BASE_URL = 'http://localhost:8080/sing-along-web-beats/api';
   ```

### Issue 2: Database Not Found
**Symptoms:** "Database connection failed"
**Solution:**
1. Buka phpMyAdmin: `http://localhost/phpmyadmin`
2. Check apakah database `karaoke_db` ada
3. Jika tidak ada, run SQL:
   ```sql
   CREATE DATABASE karaoke_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```
4. Import `database/setup_karaoke_db.sql`

### Issue 3: File Permissions
**Symptoms:** 403 Forbidden
**Solution:**
1. Check folder permissions
2. Pastikan folder `htdocs/sing-along-web-beats` readable
3. Windows: Right-click → Properties → Security
4. Linux/Mac: `chmod -R 755 htdocs/sing-along-web-beats`

### Issue 4: mod_rewrite Disabled
**Symptoms:** 404 Not Found untuk API endpoints
**Solution:**
1. Buka `xampp/apache/conf/httpd.conf`
2. Uncomment line:
   ```apache
   LoadModule rewrite_module modules/mod_rewrite.so
   ```
3. Restart Apache

### Issue 5: Antivirus/Firewall Blocking
**Symptoms:** Connection timeout
**Solution:**
1. Temporarily disable antivirus
2. Add XAMPP to firewall exceptions
3. Test again

## 🧪 Debug Commands

### Browser Console Tests:
```javascript
// Test 1: Basic API
fetch('http://localhost/sing-along-web-beats/api/debug')
  .then(r => r.json())
  .then(d => console.log('Debug:', d))
  .catch(e => console.error('Debug Error:', e));

// Test 2: CORS
fetch('http://localhost/sing-along-web-beats/api/test-cors')
  .then(r => r.json())
  .then(d => console.log('CORS:', d))
  .catch(e => console.error('CORS Error:', e));

// Test 3: Playlists
fetch('http://localhost/sing-along-web-beats/api/playlists', {
  headers: { 'X-User-ID': '1' }
})
  .then(r => r.json())
  .then(d => console.log('Playlists:', d))
  .catch(e => console.error('Playlists Error:', e));
```

### Command Line Tests:
```bash
# Test API availability
curl http://localhost/sing-along-web-beats/api/debug

# Test with headers
curl -H "X-User-ID: 1" http://localhost/sing-along-web-beats/api/playlists
```

## 📱 Alternative Solutions

### Solution 1: Use Different Port
If port 80 is busy:
1. Change Apache to port 8080
2. Update API URL in code
3. Access via `http://localhost:8080`

### Solution 2: Use IP Address
Instead of `localhost`, try:
```typescript
const API_BASE_URL = 'http://127.0.0.1/sing-along-web-beats/api';
```

### Solution 3: Disable .htaccess
If mod_rewrite issues:
1. Rename `.htaccess` to `.htaccess.bak`
2. Access direct PHP files:
   - `http://localhost/sing-along-web-beats/api/playlists.php`

## ✅ Success Indicators

When everything works:
- ✅ `http://localhost/sing-along-web-beats/api/debug` returns JSON
- ✅ No CORS errors in browser console
- ✅ Playlist tab loads without errors
- ✅ Can create new playlists
- ✅ Console shows "Playlists loaded: [...]"

## 📞 Still Not Working?

1. **Check XAMPP Error Logs:**
   - `xampp/apache/logs/error.log`
   - `xampp/mysql/data/mysql_error.log`

2. **Check Browser Network Tab:**
   - F12 → Network
   - Try loading playlist tab
   - Check failed requests

3. **Try Minimal Test:**
   Create `test.php` in `htdocs/`:
   ```php
   <?php
   header("Access-Control-Allow-Origin: *");
   echo json_encode(['status' => 'working']);
   ?>
   ```
   Access: `http://localhost/test.php`

Error "Failed to fetch" sudah diperbaiki dengan debugging tools dan better error handling! 🚀
