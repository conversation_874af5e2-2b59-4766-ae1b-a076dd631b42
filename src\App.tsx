import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import Karaoke from "./pages/Karaoke";
import NotFound from "./pages/NotFound";
import Navigation from "./components/Navigation";
import Player from "./pages/Player";

const queryClient = new QueryClient();

/**
 * Komponen utama aplikasi karaoke
 * - Mengatur routing untuk halaman utama, karaoke, dan player
 * - Menyediakan context untuk query client dan tooltip
 * - Menampilkan navigasi global dan toast notifications
 */
const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
          <Navigation />
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/karaoke" element={<Karaoke />} />
            <Route path="/player" element={<Player />} />
            <Route path="*" element={<NotFound />} />
          </Routes>
        </div>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
