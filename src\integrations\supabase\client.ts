// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://atsjzrmqjdmqfvofyukm.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF0c2p6cm1xamRtcWZ2b2Z5dWttIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkxNzg3OTUsImV4cCI6MjA2NDc1NDc5NX0.9jri_dPea4SVfXrG1vCtVEy97LI05h6NUSnCXnBkG2s";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);