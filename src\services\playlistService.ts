import type {
  Playlist,
  PlaylistVideo,
  CreatePlaylistData,
  UpdatePlaylistData,
  AddVideoToPlaylistData
} from '@/types/playlist';

// API Configuration with fallbacks
const getApiBaseUrl = () => {
  // Try different possible URLs
  const possibleUrls = [
    'http://localhost/sing-along-web-beats/api',
    'http://localhost:80/sing-along-web-beats/api',
    'http://127.0.0.1/sing-along-web-beats/api',
    'http://localhost:8080/sing-along-web-beats/api'
  ];

  // For now, return the first one, but we can add auto-detection later
  return possibleUrls[0];
};

const API_BASE_URL = getApiBaseUrl();
const DEFAULT_USER_ID = '1'; // For demo purposes

// Helper function to make API requests
async function apiRequest(endpoint: string, options: RequestInit = {}) {
  const url = `${API_BASE_URL}${endpoint}`;

  const defaultHeaders = {
    'Content-Type': 'application/json',
    'X-User-ID': DEFAULT_USER_ID,
  };

  console.log('API Request:', { url, method: options.method || 'GET' });

  try {
    const response = await fetch(url, {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    });

    console.log('API Response Status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API Error Response:', errorText);
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const data = await response.json();
    console.log('API Response Data:', data);

    return data;
  } catch (error) {
    console.error('API Request Failed:', error);

    // More specific error messages
    if (error instanceof TypeError && error.message.includes('fetch')) {
      throw new Error(`Cannot connect to API server at ${url}. Please check if XAMPP is running and the API files are in the correct location.`);
    }

    throw error;
  }
}

export class PlaylistService {
  // Debug method to test API connection
  static async testConnection(): Promise<any> {
    try {
      const response = await apiRequest('/debug');
      return response;
    } catch (error) {
      console.error('Connection test failed:', error);
      throw error;
    }
  }

  // Playlist CRUD operations
  static async createPlaylist(data: CreatePlaylistData): Promise<Playlist> {
    const response = await apiRequest('/playlists', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    return response.data;
  }

  static async getPlaylists(userId?: string): Promise<Playlist[]> {
    const response = await apiRequest('/playlists');
    return response.data || [];
  }

  static async getPlaylistById(id: string): Promise<Playlist | null> {
    try {
      const response = await apiRequest(`/playlists/${id}`);
      return response.data;
    } catch (error) {
      if (error instanceof Error && error.message.includes('404')) {
        return null;
      }
      throw error;
    }
  }

  static async updatePlaylist(id: string, data: UpdatePlaylistData): Promise<void> {
    await apiRequest('/playlists', {
      method: 'PUT',
      body: JSON.stringify({ id, ...data }),
    });
  }

  static async deletePlaylist(id: string): Promise<void> {
    await apiRequest(`/playlists?id=${id}`, {
      method: 'DELETE',
    });
  }

  // Video management
  static async addVideoToPlaylist(data: AddVideoToPlaylistData): Promise<void> {
    await apiRequest('/playlist-videos', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  static async removeVideoFromPlaylist(playlistId: string, videoId: string): Promise<void> {
    await apiRequest(`/playlist-videos?playlist_id=${playlistId}&video_id=${videoId}`, {
      method: 'DELETE',
    });
  }

  static async reorderPlaylistVideos(playlistId: string, videoIds: string[]): Promise<void> {
    const video_orders = videoIds.map((videoId, index) => ({
      video_id: videoId,
      position: index + 1
    }));

    await apiRequest('/playlist-videos', {
      method: 'PUT',
      body: JSON.stringify({
        playlist_id: playlistId,
        video_orders
      }),
    });
  }

  static async checkVideoInPlaylist(playlistId: string, videoId: string): Promise<boolean> {
    try {
      const response = await apiRequest(`/playlist-videos?playlist_id=${playlistId}`);
      const videos = response.data || [];
      return videos.some((video: any) => video.video_id === videoId);
    } catch (error) {
      return false;
    }
  }
}
