import type {
  Playlist,
  PlaylistVideo,
  CreatePlaylistData,
  UpdatePlaylistData,
  AddVideoToPlaylistData
} from '@/types/playlist';

// API Configuration
const API_BASE_URL = 'http://localhost/sing-along-web-beats/api';
const DEFAULT_USER_ID = '1'; // For demo purposes

// Helper function to make API requests
async function apiRequest(endpoint: string, options: RequestInit = {}) {
  const url = `${API_BASE_URL}${endpoint}`;

  const defaultHeaders = {
    'Content-Type': 'application/json',
    'X-User-ID': DEFAULT_USER_ID,
  };

  const response = await fetch(url, {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  });

  const data = await response.json();

  if (!response.ok) {
    throw new Error(data.message || `HTTP error! status: ${response.status}`);
  }

  return data;
}

export class PlaylistService {
  // Playlist CRUD operations
  static async createPlaylist(data: CreatePlaylistData): Promise<Playlist> {
    const response = await apiRequest('/playlists', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    return response.data;
  }

  static async getPlaylists(userId?: string): Promise<Playlist[]> {
    const response = await apiRequest('/playlists');
    return response.data || [];
  }

  static async getPlaylistById(id: string): Promise<Playlist | null> {
    try {
      const response = await apiRequest(`/playlists/${id}`);
      return response.data;
    } catch (error) {
      if (error instanceof Error && error.message.includes('404')) {
        return null;
      }
      throw error;
    }
  }

  static async updatePlaylist(id: string, data: UpdatePlaylistData): Promise<void> {
    await apiRequest('/playlists', {
      method: 'PUT',
      body: JSON.stringify({ id, ...data }),
    });
  }

  static async deletePlaylist(id: string): Promise<void> {
    await apiRequest(`/playlists?id=${id}`, {
      method: 'DELETE',
    });
  }

  // Video management
  static async addVideoToPlaylist(data: AddVideoToPlaylistData): Promise<void> {
    await apiRequest('/playlist-videos', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  static async removeVideoFromPlaylist(playlistId: string, videoId: string): Promise<void> {
    await apiRequest(`/playlist-videos?playlist_id=${playlistId}&video_id=${videoId}`, {
      method: 'DELETE',
    });
  }

  static async reorderPlaylistVideos(playlistId: string, videoIds: string[]): Promise<void> {
    const video_orders = videoIds.map((videoId, index) => ({
      video_id: videoId,
      position: index + 1
    }));

    await apiRequest('/playlist-videos', {
      method: 'PUT',
      body: JSON.stringify({
        playlist_id: playlistId,
        video_orders
      }),
    });
  }

  static async checkVideoInPlaylist(playlistId: string, videoId: string): Promise<boolean> {
    try {
      const response = await apiRequest(`/playlist-videos?playlist_id=${playlistId}`);
      const videos = response.data || [];
      return videos.some((video: any) => video.video_id === videoId);
    } catch (error) {
      return false;
    }
  }
}
