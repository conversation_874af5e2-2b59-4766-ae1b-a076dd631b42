import { supabase } from '@/integrations/supabase/client';
import type { 
  Playlist, 
  PlaylistVideo, 
  CreatePlaylistData, 
  UpdatePlaylistData, 
  AddVideoToPlaylistData 
} from '@/types/playlist';

export class PlaylistService {
  // Playlist CRUD operations
  static async createPlaylist(data: CreatePlaylistData): Promise<Playlist> {
    const { data: playlist, error } = await supabase
      .from('playlists')
      .insert({
        name: data.name,
        description: data.description,
        is_public: data.is_public ?? false,
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create playlist: ${error.message}`);
    }

    return playlist;
  }

  static async getPlaylists(userId?: string): Promise<Playlist[]> {
    let query = supabase
      .from('playlists')
      .select(`
        *,
        playlist_videos (
          id,
          video_id,
          video_title,
          video_thumbnail,
          channel_title,
          duration,
          position,
          added_at
        )
      `)
      .order('updated_at', { ascending: false });

    if (userId) {
      query = query.eq('user_id', userId);
    } else {
      query = query.eq('is_public', true);
    }

    const { data: playlists, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch playlists: ${error.message}`);
    }

    return playlists?.map(playlist => ({
      ...playlist,
      videos: playlist.playlist_videos?.sort((a, b) => a.position - b.position) || [],
      video_count: playlist.playlist_videos?.length || 0
    })) || [];
  }

  static async getPlaylistById(id: string): Promise<Playlist | null> {
    const { data: playlist, error } = await supabase
      .from('playlists')
      .select(`
        *,
        playlist_videos (
          id,
          video_id,
          video_title,
          video_thumbnail,
          channel_title,
          duration,
          position,
          added_at
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Playlist not found
      }
      throw new Error(`Failed to fetch playlist: ${error.message}`);
    }

    return {
      ...playlist,
      videos: playlist.playlist_videos?.sort((a, b) => a.position - b.position) || [],
      video_count: playlist.playlist_videos?.length || 0
    };
  }

  static async updatePlaylist(id: string, data: UpdatePlaylistData): Promise<void> {
    const { error } = await supabase
      .from('playlists')
      .update({
        ...data,
        updated_at: new Date().toISOString()
      })
      .eq('id', id);

    if (error) {
      throw new Error(`Failed to update playlist: ${error.message}`);
    }
  }

  static async deletePlaylist(id: string): Promise<void> {
    // First delete all videos in the playlist
    await supabase
      .from('playlist_videos')
      .delete()
      .eq('playlist_id', id);

    // Then delete the playlist
    const { error } = await supabase
      .from('playlists')
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(`Failed to delete playlist: ${error.message}`);
    }
  }

  // Video management
  static async addVideoToPlaylist(data: AddVideoToPlaylistData): Promise<void> {
    // Get the current max position for this playlist
    const { data: maxPosition } = await supabase
      .from('playlist_videos')
      .select('position')
      .eq('playlist_id', data.playlist_id)
      .order('position', { ascending: false })
      .limit(1)
      .single();

    const position = data.position ?? (maxPosition?.position ?? 0) + 1;

    const { error } = await supabase
      .from('playlist_videos')
      .insert({
        playlist_id: data.playlist_id,
        video_id: data.video.id,
        video_title: data.video.title,
        video_thumbnail: data.video.thumbnail,
        channel_title: data.video.channelTitle,
        duration: data.video.duration,
        position
      });

    if (error) {
      throw new Error(`Failed to add video to playlist: ${error.message}`);
    }

    // Update playlist's updated_at timestamp
    await this.updatePlaylist(data.playlist_id, {});
  }

  static async removeVideoFromPlaylist(playlistId: string, videoId: string): Promise<void> {
    const { error } = await supabase
      .from('playlist_videos')
      .delete()
      .eq('playlist_id', playlistId)
      .eq('video_id', videoId);

    if (error) {
      throw new Error(`Failed to remove video from playlist: ${error.message}`);
    }

    // Update playlist's updated_at timestamp
    await this.updatePlaylist(playlistId, {});
  }

  static async reorderPlaylistVideos(playlistId: string, videoIds: string[]): Promise<void> {
    const updates = videoIds.map((videoId, index) => ({
      playlist_id: playlistId,
      video_id: videoId,
      position: index + 1
    }));

    for (const update of updates) {
      await supabase
        .from('playlist_videos')
        .update({ position: update.position })
        .eq('playlist_id', update.playlist_id)
        .eq('video_id', update.video_id);
    }

    // Update playlist's updated_at timestamp
    await this.updatePlaylist(playlistId, {});
  }

  static async checkVideoInPlaylist(playlistId: string, videoId: string): Promise<boolean> {
    const { data, error } = await supabase
      .from('playlist_videos')
      .select('id')
      .eq('playlist_id', playlistId)
      .eq('video_id', videoId)
      .single();

    return !error && !!data;
  }
}
