import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Plus, Search, Music, Play, Trash2, Edit, Lock, Unlock, AlertCircle } from "lucide-react";
import { usePlaylist } from "@/contexts/PlaylistContext";
import { PlaylistService } from "@/services/playlistService";
import { toast } from "sonner";
import type { Playlist } from "@/types/playlist";

interface PlaylistManagerProps {
  onPlaylistSelect?: (playlist: Playlist) => void;
}

/**
 * Komponen utama untuk mengelola playlist YouTube
 * - Menampilkan daftar playlist pengguna
 * - Membuat playlist baru
 * - Mengedit dan menghapus playlist
 * - <PERSON><PERSON><PERSON> playlist
 */
const PlaylistManager = ({ onPlaylistSelect }: PlaylistManagerProps) => {
  const { 
    playlists, 
    loading, 
    error, 
    createPlaylist, 
    updatePlaylist, 
    deletePlaylist,
    playPlaylist 
  } = usePlaylist();

  const [searchQuery, setSearchQuery] = useState("");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingPlaylist, setEditingPlaylist] = useState<Playlist | null>(null);
  const [newPlaylistName, setNewPlaylistName] = useState("");
  const [newPlaylistDescription, setNewPlaylistDescription] = useState("");
  const [newPlaylistIsPublic, setNewPlaylistIsPublic] = useState(false);
  const [connectionTesting, setConnectionTesting] = useState(false);

  // Filter playlists based on search query
  const filteredPlaylists = playlists.filter(playlist =>
    playlist.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    playlist.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleCreatePlaylist = async () => {
    if (!newPlaylistName.trim()) {
      toast.error("Nama playlist tidak boleh kosong");
      return;
    }

    try {
      await createPlaylist({
        name: newPlaylistName.trim(),
        description: newPlaylistDescription.trim() || undefined,
        is_public: newPlaylistIsPublic
      });
      
      toast.success("Playlist berhasil dibuat!");
      setIsCreateDialogOpen(false);
      setNewPlaylistName("");
      setNewPlaylistDescription("");
      setNewPlaylistIsPublic(false);
    } catch (error) {
      toast.error("Gagal membuat playlist");
    }
  };

  const handleUpdatePlaylist = async () => {
    if (!editingPlaylist || !newPlaylistName.trim()) return;

    try {
      await updatePlaylist(editingPlaylist.id, {
        name: newPlaylistName.trim(),
        description: newPlaylistDescription.trim() || undefined,
        is_public: newPlaylistIsPublic
      });
      
      toast.success("Playlist berhasil diperbarui!");
      setEditingPlaylist(null);
      setNewPlaylistName("");
      setNewPlaylistDescription("");
      setNewPlaylistIsPublic(false);
    } catch (error) {
      toast.error("Gagal memperbarui playlist");
    }
  };

  const handleDeletePlaylist = async (playlist: Playlist) => {
    if (!confirm(`Apakah Anda yakin ingin menghapus playlist "${playlist.name}"?`)) {
      return;
    }

    try {
      await deletePlaylist(playlist.id);
      toast.success("Playlist berhasil dihapus!");
    } catch (error) {
      toast.error("Gagal menghapus playlist");
    }
  };

  const handleEditPlaylist = (playlist: Playlist) => {
    setEditingPlaylist(playlist);
    setNewPlaylistName(playlist.name);
    setNewPlaylistDescription(playlist.description || "");
    setNewPlaylistIsPublic(playlist.is_public);
  };

  const handlePlayPlaylist = (playlist: Playlist) => {
    if (playlist.videos && playlist.videos.length > 0) {
      playPlaylist(playlist);
      onPlaylistSelect?.(playlist);
    } else {
      toast.info("Playlist kosong. Tambahkan video terlebih dahulu.");
    }
  };

  const handleTestConnection = async () => {
    setConnectionTesting(true);
    try {
      const result = await PlaylistService.testConnection();
      console.log('Connection test result:', result);
      toast.success("API connection successful!");
    } catch (error) {
      console.error('Connection test failed:', error);
      toast.error(`API connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setConnectionTesting(false);
    }
  };

  if (error) {
    return (
      <Card className="bg-white/10 backdrop-blur-md border-white/20">
        <CardContent className="p-6">
          <div className="text-center">
            <AlertCircle className="w-12 h-12 text-red-400 mx-auto mb-4" />
            <h3 className="text-white text-lg font-semibold mb-2">Connection Error</h3>
            <p className="text-red-400 mb-4">{error}</p>
            <div className="space-y-2">
              <Button
                onClick={handleTestConnection}
                disabled={connectionTesting}
                className="bg-blue-500 hover:bg-blue-600"
              >
                {connectionTesting ? "Testing..." : "Test API Connection"}
              </Button>
              <div className="text-sm text-white/60">
                <p>Make sure XAMPP is running and API files are in place:</p>
                <p className="font-mono text-xs">htdocs/sing-along-web-beats/api/</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header with Search and Create Button */}
      <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 w-4 h-4" />
          <Input
            placeholder="Cari playlist..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-white/60"
          />
        </div>

        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600">
              <Plus className="w-4 h-4 mr-2" />
              Buat Playlist
            </Button>
          </DialogTrigger>
          <DialogContent className="bg-gray-900 border-gray-700">
            <DialogHeader>
              <DialogTitle className="text-white">Buat Playlist Baru</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <label className="text-white text-sm font-medium">Nama Playlist</label>
                <Input
                  placeholder="Masukkan nama playlist"
                  value={newPlaylistName}
                  onChange={(e) => setNewPlaylistName(e.target.value)}
                  className="mt-1 bg-white/10 border-white/20 text-white"
                />
              </div>
              <div>
                <label className="text-white text-sm font-medium">Deskripsi (Opsional)</label>
                <Textarea
                  placeholder="Masukkan deskripsi playlist"
                  value={newPlaylistDescription}
                  onChange={(e) => setNewPlaylistDescription(e.target.value)}
                  className="mt-1 bg-white/10 border-white/20 text-white"
                  rows={3}
                />
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="isPublic"
                  checked={newPlaylistIsPublic}
                  onChange={(e) => setNewPlaylistIsPublic(e.target.checked)}
                  className="rounded"
                />
                <label htmlFor="isPublic" className="text-white text-sm">
                  Jadikan playlist publik
                </label>
              </div>
              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setIsCreateDialogOpen(false)}
                  className="border-white/20 text-white hover:bg-white/10"
                >
                  Batal
                </Button>
                <Button
                  onClick={handleCreatePlaylist}
                  className="bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600"
                >
                  Buat Playlist
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Playlists Grid */}
      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
          <p className="text-white/60 mt-2">Memuat playlist...</p>
        </div>
      ) : filteredPlaylists.length === 0 ? (
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardContent className="p-8 text-center">
            <Music className="w-16 h-16 mx-auto mb-4 text-white/40" />
            <p className="text-white/60 mb-4">
              {searchQuery ? "Tidak ada playlist yang ditemukan" : "Belum ada playlist"}
            </p>
            {!searchQuery && (
              <Button
                onClick={() => setIsCreateDialogOpen(true)}
                className="bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600"
              >
                <Plus className="w-4 h-4 mr-2" />
                Buat Playlist Pertama
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredPlaylists.map((playlist) => (
            <Card
              key={playlist.id}
              className="bg-white/10 backdrop-blur-md border-white/20 hover:bg-white/15 transition-all duration-300 cursor-pointer group"
              onClick={() => onPlaylistSelect?.(playlist)}
            >
              <CardHeader className="pb-2">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-white text-lg truncate flex items-center gap-2">
                      {playlist.name}
                      {playlist.is_public ? (
                        <Unlock className="w-4 h-4 text-green-400" />
                      ) : (
                        <Lock className="w-4 h-4 text-gray-400" />
                      )}
                    </CardTitle>
                    {playlist.description && (
                      <p className="text-white/70 text-sm mt-1 line-clamp-2">
                        {playlist.description}
                      </p>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="text-white/60 text-sm">
                    {playlist.video_count || 0} video{(playlist.video_count || 0) !== 1 ? 's' : ''}
                  </div>
                  <div className="flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={(e) => {
                        e.stopPropagation();
                        handlePlayPlaylist(playlist);
                      }}
                      className="text-white hover:bg-white/20"
                    >
                      <Play className="w-4 h-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEditPlaylist(playlist);
                      }}
                      className="text-white hover:bg-white/20"
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeletePlaylist(playlist);
                      }}
                      className="text-white hover:bg-red-500/20 hover:text-red-400"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Edit Playlist Dialog */}
      <Dialog open={!!editingPlaylist} onOpenChange={() => setEditingPlaylist(null)}>
        <DialogContent className="bg-gray-900 border-gray-700">
          <DialogHeader>
            <DialogTitle className="text-white">Edit Playlist</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-white text-sm font-medium">Nama Playlist</label>
              <Input
                placeholder="Masukkan nama playlist"
                value={newPlaylistName}
                onChange={(e) => setNewPlaylistName(e.target.value)}
                className="mt-1 bg-white/10 border-white/20 text-white"
              />
            </div>
            <div>
              <label className="text-white text-sm font-medium">Deskripsi (Opsional)</label>
              <Textarea
                placeholder="Masukkan deskripsi playlist"
                value={newPlaylistDescription}
                onChange={(e) => setNewPlaylistDescription(e.target.value)}
                className="mt-1 bg-white/10 border-white/20 text-white"
                rows={3}
              />
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="editIsPublic"
                checked={newPlaylistIsPublic}
                onChange={(e) => setNewPlaylistIsPublic(e.target.checked)}
                className="rounded"
              />
              <label htmlFor="editIsPublic" className="text-white text-sm">
                Jadikan playlist publik
              </label>
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setEditingPlaylist(null)}
                className="border-white/20 text-white hover:bg-white/10"
              >
                Batal
              </Button>
              <Button
                onClick={handleUpdatePlaylist}
                className="bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600"
              >
                Simpan Perubahan
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PlaylistManager;
