-- Create playlists table
CREATE TABLE IF NOT EXISTS playlists (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_public BOOLEAN DEFAULT FALSE,
    thumbnail_url TEXT
);

-- Create playlist_videos table
CREATE TABLE IF NOT EXISTS playlist_videos (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    playlist_id UUID REFERENCES playlists(id) ON DELETE CASCADE,
    video_id VARCHAR(255) NOT NULL,
    video_title TEXT NOT NULL,
    video_thumbnail TEXT NOT NULL,
    channel_title VARCHAR(255) NOT NULL,
    duration VARCHAR(50),
    position INTEGER NOT NULL DEFAULT 1,
    added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_playlists_user_id ON playlists(user_id);
CREATE INDEX IF NOT EXISTS idx_playlists_is_public ON playlists(is_public);
CREATE INDEX IF NOT EXISTS idx_playlists_updated_at ON playlists(updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_playlist_videos_playlist_id ON playlist_videos(playlist_id);
CREATE INDEX IF NOT EXISTS idx_playlist_videos_position ON playlist_videos(playlist_id, position);
CREATE INDEX IF NOT EXISTS idx_playlist_videos_video_id ON playlist_videos(video_id);

-- Create unique constraint to prevent duplicate videos in the same playlist
CREATE UNIQUE INDEX IF NOT EXISTS idx_playlist_videos_unique 
ON playlist_videos(playlist_id, video_id);

-- Enable Row Level Security (RLS)
ALTER TABLE playlists ENABLE ROW LEVEL SECURITY;
ALTER TABLE playlist_videos ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for playlists
CREATE POLICY "Users can view their own playlists" ON playlists
    FOR SELECT USING (auth.uid() = user_id OR is_public = true);

CREATE POLICY "Users can create their own playlists" ON playlists
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own playlists" ON playlists
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own playlists" ON playlists
    FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for playlist_videos
CREATE POLICY "Users can view videos in accessible playlists" ON playlist_videos
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM playlists 
            WHERE playlists.id = playlist_videos.playlist_id 
            AND (playlists.user_id = auth.uid() OR playlists.is_public = true)
        )
    );

CREATE POLICY "Users can add videos to their own playlists" ON playlist_videos
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM playlists 
            WHERE playlists.id = playlist_videos.playlist_id 
            AND playlists.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update videos in their own playlists" ON playlist_videos
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM playlists 
            WHERE playlists.id = playlist_videos.playlist_id 
            AND playlists.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete videos from their own playlists" ON playlist_videos
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM playlists 
            WHERE playlists.id = playlist_videos.playlist_id 
            AND playlists.user_id = auth.uid()
        )
    );

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at on playlists
CREATE TRIGGER update_playlists_updated_at 
    BEFORE UPDATE ON playlists 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to update playlist updated_at when videos are modified
CREATE OR REPLACE FUNCTION update_playlist_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE playlists 
    SET updated_at = NOW() 
    WHERE id = COALESCE(NEW.playlist_id, OLD.playlist_id);
    RETURN COALESCE(NEW, OLD);
END;
$$ language 'plpgsql';

-- Create triggers to update playlist timestamp when videos are modified
CREATE TRIGGER update_playlist_on_video_insert
    AFTER INSERT ON playlist_videos
    FOR EACH ROW
    EXECUTE FUNCTION update_playlist_timestamp();

CREATE TRIGGER update_playlist_on_video_update
    AFTER UPDATE ON playlist_videos
    FOR EACH ROW
    EXECUTE FUNCTION update_playlist_timestamp();

CREATE TRIGGER update_playlist_on_video_delete
    AFTER DELETE ON playlist_videos
    FOR EACH ROW
    EXECUTE FUNCTION update_playlist_timestamp();
