import { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Plus, Check, Search, Music } from "lucide-react";
import { usePlaylist } from "@/contexts/PlaylistContext";
import { toast } from "sonner";
import type { YouTubeVideo, Playlist } from "@/types/playlist";

interface AddToPlaylistDialogProps {
  video: YouTubeVideo | null;
  isOpen: boolean;
  onClose: () => void;
}

/**
 * Dialog untuk menambahkan video ke playlist
 * - Menampilkan daftar playlist yang tersedia
 * - Membuat playlist baru langsung dari dialog
 * - Mencari playlist
 * - Menambahkan video ke playlist yang dipilih
 */
const AddToPlaylistDialog = ({ video, isOpen, onClose }: AddToPlaylistDialogProps) => {
  const { 
    playlists, 
    createPlaylist, 
    addVideoToPlaylist, 
    loading 
  } = usePlaylist();

  const [searchQuery, setSearchQuery] = useState("");
  const [isCreatingPlaylist, setIsCreatingPlaylist] = useState(false);
  const [newPlaylistName, setNewPlaylistName] = useState("");
  const [addingToPlaylist, setAddingToPlaylist] = useState<string | null>(null);

  // Reset state when dialog closes
  useEffect(() => {
    if (!isOpen) {
      setSearchQuery("");
      setIsCreatingPlaylist(false);
      setNewPlaylistName("");
      setAddingToPlaylist(null);
    }
  }, [isOpen]);

  // Filter playlists based on search query
  const filteredPlaylists = playlists.filter(playlist =>
    playlist.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleCreateAndAddToPlaylist = async () => {
    if (!newPlaylistName.trim() || !video) return;

    try {
      setAddingToPlaylist("creating");
      
      // Create new playlist
      const newPlaylist = await createPlaylist({
        name: newPlaylistName.trim(),
        is_public: false
      });

      // Add video to the new playlist
      await addVideoToPlaylist({
        playlist_id: newPlaylist.id,
        video: video
      });

      toast.success(`Video ditambahkan ke playlist "${newPlaylist.name}"`);
      onClose();
    } catch (error) {
      toast.error("Gagal membuat playlist dan menambahkan video");
    } finally {
      setAddingToPlaylist(null);
    }
  };

  const handleAddToExistingPlaylist = async (playlist: Playlist) => {
    if (!video) return;

    try {
      setAddingToPlaylist(playlist.id);
      
      await addVideoToPlaylist({
        playlist_id: playlist.id,
        video: video
      });

      toast.success(`Video ditambahkan ke playlist "${playlist.name}"`);
      onClose();
    } catch (error) {
      toast.error("Gagal menambahkan video ke playlist");
    } finally {
      setAddingToPlaylist(null);
    }
  };

  if (!video) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-gray-900 border-gray-700 max-w-md">
        <DialogHeader>
          <DialogTitle className="text-white">Tambah ke Playlist</DialogTitle>
        </DialogHeader>

        {/* Video Info */}
        <div className="flex items-center space-x-3 p-3 bg-white/5 rounded-lg">
          <img
            src={video.thumbnail}
            alt={video.title}
            className="w-16 h-12 object-cover rounded"
          />
          <div className="flex-1 min-w-0">
            <h4 className="text-white font-medium text-sm truncate">
              {video.title}
            </h4>
            <p className="text-white/70 text-xs truncate">
              {video.channelTitle}
            </p>
          </div>
        </div>

        {/* Search Playlists */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 w-4 h-4" />
          <Input
            placeholder="Cari playlist..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-white/60"
          />
        </div>

        {/* Create New Playlist */}
        <div className="space-y-2">
          {!isCreatingPlaylist ? (
            <Button
              variant="outline"
              onClick={() => setIsCreatingPlaylist(true)}
              className="w-full border-white/20 text-white hover:bg-white/10"
            >
              <Plus className="w-4 h-4 mr-2" />
              Buat Playlist Baru
            </Button>
          ) : (
            <div className="space-y-2">
              <Input
                placeholder="Nama playlist baru"
                value={newPlaylistName}
                onChange={(e) => setNewPlaylistName(e.target.value)}
                className="bg-white/10 border-white/20 text-white"
                autoFocus
              />
              <div className="flex space-x-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    setIsCreatingPlaylist(false);
                    setNewPlaylistName("");
                  }}
                  className="flex-1 border-white/20 text-white hover:bg-white/10"
                >
                  Batal
                </Button>
                <Button
                  size="sm"
                  onClick={handleCreateAndAddToPlaylist}
                  disabled={!newPlaylistName.trim() || addingToPlaylist === "creating"}
                  className="flex-1 bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600"
                >
                  {addingToPlaylist === "creating" ? (
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  ) : (
                    <>
                      <Plus className="w-4 h-4 mr-1" />
                      Buat & Tambah
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Existing Playlists */}
        <div className="space-y-2">
          <h4 className="text-white font-medium text-sm">Playlist Tersedia</h4>
          <ScrollArea className="h-48">
            {loading ? (
              <div className="text-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white mx-auto"></div>
                <p className="text-white/60 text-sm mt-2">Memuat playlist...</p>
              </div>
            ) : filteredPlaylists.length === 0 ? (
              <div className="text-center py-4">
                <Music className="w-8 h-8 mx-auto mb-2 text-white/40" />
                <p className="text-white/60 text-sm">
                  {searchQuery ? "Tidak ada playlist yang ditemukan" : "Belum ada playlist"}
                </p>
              </div>
            ) : (
              <div className="space-y-1">
                {filteredPlaylists.map((playlist) => (
                  <Button
                    key={playlist.id}
                    variant="ghost"
                    onClick={() => handleAddToExistingPlaylist(playlist)}
                    disabled={addingToPlaylist === playlist.id}
                    className="w-full justify-start text-left h-auto p-3 hover:bg-white/10"
                  >
                    <div className="flex items-center justify-between w-full">
                      <div className="flex-1 min-w-0">
                        <p className="text-white font-medium truncate">
                          {playlist.name}
                        </p>
                        <p className="text-white/60 text-xs">
                          {playlist.video_count || 0} video{(playlist.video_count || 0) !== 1 ? 's' : ''}
                        </p>
                      </div>
                      <div className="ml-2">
                        {addingToPlaylist === playlist.id ? (
                          <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                        ) : (
                          <Plus className="w-4 h-4 text-white/60" />
                        )}
                      </div>
                    </div>
                  </Button>
                ))}
              </div>
            )}
          </ScrollArea>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AddToPlaylistDialog;
