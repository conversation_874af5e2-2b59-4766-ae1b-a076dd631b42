-- =====================================================
-- Setup Database karaoke_db untuk XAMPP
-- =====================================================

-- Buat database
CREATE DATABASE IF NOT EXISTS karaoke_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Gunakan database
USE karaoke_db;

-- =====================================================
-- Users Table
-- =====================================================
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(36) UNIQUE NOT NULL DEFAULT (UUID()),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    avatar_url TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    
    INDEX idx_users_uuid (uuid),
    INDEX idx_users_username (username),
    INDEX idx_users_email (email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- Playlists Table
-- =====================================================
CREATE TABLE IF NOT EXISTS playlists (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(36) UNIQUE NOT NULL DEFAULT (UUID()),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    user_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_public BOOLEAN DEFAULT FALSE,
    thumbnail_url TEXT,
    video_count INT DEFAULT 0,
    total_duration INT DEFAULT 0,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_playlists_user_id (user_id),
    INDEX idx_playlists_is_public (is_public),
    INDEX idx_playlists_updated_at (updated_at DESC),
    INDEX idx_playlists_uuid (uuid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- Playlist Videos Table
-- =====================================================
CREATE TABLE IF NOT EXISTS playlist_videos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(36) UNIQUE NOT NULL DEFAULT (UUID()),
    playlist_id INT NOT NULL,
    video_id VARCHAR(255) NOT NULL,
    video_title TEXT NOT NULL,
    video_thumbnail TEXT NOT NULL,
    channel_title VARCHAR(255) NOT NULL,
    duration VARCHAR(50),
    duration_seconds INT DEFAULT 0,
    position INT NOT NULL DEFAULT 1,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (playlist_id) REFERENCES playlists(id) ON DELETE CASCADE,
    UNIQUE KEY unique_playlist_video (playlist_id, video_id),
    
    INDEX idx_playlist_videos_playlist_id (playlist_id),
    INDEX idx_playlist_videos_position (playlist_id, position),
    INDEX idx_playlist_videos_video_id (video_id),
    INDEX idx_playlist_videos_uuid (uuid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- Insert Sample Data
-- =====================================================

-- Insert demo user
INSERT INTO users (username, email, password_hash, full_name) VALUES 
('demo_user', '<EMAIL>', '$2y$10$example_hash_demo_user', 'Demo User')
ON DUPLICATE KEY UPDATE username = username;

-- Get demo user ID
SET @demo_user_id = (SELECT id FROM users WHERE username = 'demo_user' LIMIT 1);

-- Insert sample playlists
INSERT INTO playlists (name, description, user_id, is_public) VALUES 
('My Favorite Karaoke Songs', 'Koleksi lagu karaoke favorit saya', @demo_user_id, TRUE),
('Rock Classics', 'Lagu rock klasik untuk karaoke', @demo_user_id, TRUE),
('Pop Hits Indonesia', 'Lagu pop Indonesia populer', @demo_user_id, FALSE)
ON DUPLICATE KEY UPDATE name = name;

-- =====================================================
-- Triggers untuk Auto Update Stats
-- =====================================================

DELIMITER $$

CREATE TRIGGER IF NOT EXISTS update_playlist_stats_after_insert
AFTER INSERT ON playlist_videos
FOR EACH ROW
BEGIN
    UPDATE playlists 
    SET 
        video_count = (
            SELECT COUNT(*) 
            FROM playlist_videos 
            WHERE playlist_id = NEW.playlist_id
        ),
        total_duration = (
            SELECT COALESCE(SUM(duration_seconds), 0) 
            FROM playlist_videos 
            WHERE playlist_id = NEW.playlist_id
        ),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.playlist_id;
END$$

CREATE TRIGGER IF NOT EXISTS update_playlist_stats_after_delete
AFTER DELETE ON playlist_videos
FOR EACH ROW
BEGIN
    UPDATE playlists 
    SET 
        video_count = (
            SELECT COUNT(*) 
            FROM playlist_videos 
            WHERE playlist_id = OLD.playlist_id
        ),
        total_duration = (
            SELECT COALESCE(SUM(duration_seconds), 0) 
            FROM playlist_videos 
            WHERE playlist_id = OLD.playlist_id
        ),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = OLD.playlist_id;
END$$

DELIMITER ;

-- =====================================================
-- Verify Setup
-- =====================================================

-- Show created tables
SHOW TABLES;

-- Show sample data
SELECT 'Users:' as Info;
SELECT id, username, email, full_name, created_at FROM users;

SELECT 'Playlists:' as Info;
SELECT id, name, description, user_id, is_public, video_count, created_at FROM playlists;

-- Success message
SELECT 'Database karaoke_db berhasil dibuat!' as Status;
