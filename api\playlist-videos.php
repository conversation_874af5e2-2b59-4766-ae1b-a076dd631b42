<?php
/**
 * Playlist Videos API Endpoints
 * Handles adding, removing, and reordering videos in playlists
 */

require_once 'config/database.php';

// Set CORS headers
setCorsHeaders();

// Get database connection
$database = new Database();
$db = $database->getConnection();

if (!$db) {
    errorResponse('Database connection failed', 500);
}

// Get request method and data
$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);
$user_id = getCurrentUserId();

// Log the request
logRequest('/api/playlist-videos', $method, $user_id);

switch ($method) {
    case 'GET':
        getPlaylistVideos($db);
        break;
    case 'POST':
        addVideoToPlaylist($db, $input, $user_id);
        break;
    case 'PUT':
        reorderPlaylistVideos($db, $input, $user_id);
        break;
    case 'DELETE':
        removeVideoFromPlaylist($db, $user_id);
        break;
    default:
        errorResponse('Method not allowed', 405);
}

/**
 * Get videos for a specific playlist
 */
function getPlaylistVideos($db) {
    try {
        $playlist_id = isset($_GET['playlist_id']) ? (int)$_GET['playlist_id'] : null;
        
        if (!$playlist_id) {
            errorResponse('Playlist ID is required', 400);
        }
        
        $query = "
            SELECT 
                pv.id,
                pv.uuid,
                pv.video_id,
                pv.video_title,
                pv.video_thumbnail,
                pv.channel_title,
                pv.duration,
                pv.duration_seconds,
                pv.position,
                pv.added_at,
                p.name as playlist_name,
                p.user_id as playlist_owner
            FROM playlist_videos pv
            JOIN playlists p ON pv.playlist_id = p.id
            WHERE pv.playlist_id = :playlist_id
            ORDER BY pv.position ASC
        ";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':playlist_id', $playlist_id);
        $stmt->execute();
        
        $videos = $stmt->fetchAll();
        
        successResponse($videos);
        
    } catch (Exception $e) {
        errorResponse('Failed to fetch playlist videos: ' . $e->getMessage(), 500);
    }
}

/**
 * Add video to playlist
 */
function addVideoToPlaylist($db, $input, $user_id) {
    try {
        validateRequired($input, ['playlist_id', 'video']);
        
        $playlist_id = (int)$input['playlist_id'];
        $video = $input['video'];
        
        // Validate video data
        validateRequired($video, ['id', 'title', 'thumbnail', 'channelTitle']);
        
        // Check playlist ownership
        checkPlaylistOwnership($playlist_id, $user_id, $db);
        
        // Check if video already exists in playlist
        $check_query = "SELECT id FROM playlist_videos WHERE playlist_id = :playlist_id AND video_id = :video_id";
        $check_stmt = $db->prepare($check_query);
        $check_stmt->bindParam(':playlist_id', $playlist_id);
        $check_stmt->bindParam(':video_id', $video['id']);
        $check_stmt->execute();
        
        if ($check_stmt->fetch()) {
            errorResponse('Video already exists in playlist', 409);
        }
        
        // Get next position
        $position_query = "SELECT COALESCE(MAX(position), 0) + 1 as next_position FROM playlist_videos WHERE playlist_id = :playlist_id";
        $position_stmt = $db->prepare($position_query);
        $position_stmt->bindParam(':playlist_id', $playlist_id);
        $position_stmt->execute();
        $position_result = $position_stmt->fetch();
        $position = isset($input['position']) ? (int)$input['position'] : $position_result['next_position'];
        
        // Parse duration
        $duration = isset($video['duration']) ? $video['duration'] : null;
        $duration_seconds = 0;
        if ($duration) {
            try {
                $duration_seconds = parseYouTubeDuration($duration);
            } catch (Exception $e) {
                // If parsing fails, keep duration_seconds as 0
            }
        }
        
        $uuid = generateUUID();
        
        // Insert video
        $query = "
            INSERT INTO playlist_videos (
                uuid, playlist_id, video_id, video_title, video_thumbnail, 
                channel_title, duration, duration_seconds, position
            ) VALUES (
                :uuid, :playlist_id, :video_id, :video_title, :video_thumbnail,
                :channel_title, :duration, :duration_seconds, :position
            )
        ";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':uuid', $uuid);
        $stmt->bindParam(':playlist_id', $playlist_id);
        $stmt->bindParam(':video_id', $video['id']);
        $stmt->bindParam(':video_title', $video['title']);
        $stmt->bindParam(':video_thumbnail', $video['thumbnail']);
        $stmt->bindParam(':channel_title', $video['channelTitle']);
        $stmt->bindParam(':duration', $duration);
        $stmt->bindParam(':duration_seconds', $duration_seconds);
        $stmt->bindParam(':position', $position);
        
        if ($stmt->execute()) {
            $video_id = $db->lastInsertId();
            
            // Get the added video
            $get_query = "SELECT * FROM playlist_videos WHERE id = :id";
            $get_stmt = $db->prepare($get_query);
            $get_stmt->bindParam(':id', $video_id);
            $get_stmt->execute();
            
            $added_video = $get_stmt->fetch();
            
            successResponse($added_video, 'Video added to playlist successfully');
        } else {
            errorResponse('Failed to add video to playlist', 500);
        }
        
    } catch (Exception $e) {
        errorResponse('Failed to add video to playlist: ' . $e->getMessage(), 500);
    }
}

/**
 * Remove video from playlist
 */
function removeVideoFromPlaylist($db, $user_id) {
    try {
        $playlist_id = isset($_GET['playlist_id']) ? (int)$_GET['playlist_id'] : null;
        $video_id = isset($_GET['video_id']) ? $_GET['video_id'] : null;
        
        if (!$playlist_id || !$video_id) {
            errorResponse('Playlist ID and Video ID are required', 400);
        }
        
        // Check playlist ownership
        checkPlaylistOwnership($playlist_id, $user_id, $db);
        
        // Delete video
        $query = "DELETE FROM playlist_videos WHERE playlist_id = :playlist_id AND video_id = :video_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':playlist_id', $playlist_id);
        $stmt->bindParam(':video_id', $video_id);
        
        if ($stmt->execute()) {
            if ($stmt->rowCount() > 0) {
                successResponse(null, 'Video removed from playlist successfully');
            } else {
                errorResponse('Video not found in playlist', 404);
            }
        } else {
            errorResponse('Failed to remove video from playlist', 500);
        }
        
    } catch (Exception $e) {
        errorResponse('Failed to remove video from playlist: ' . $e->getMessage(), 500);
    }
}

/**
 * Reorder videos in playlist
 */
function reorderPlaylistVideos($db, $input, $user_id) {
    try {
        validateRequired($input, ['playlist_id', 'video_orders']);
        
        $playlist_id = (int)$input['playlist_id'];
        $video_orders = $input['video_orders']; // Array of {video_id, position}
        
        // Check playlist ownership
        checkPlaylistOwnership($playlist_id, $user_id, $db);
        
        // Start transaction
        $db->beginTransaction();
        
        try {
            foreach ($video_orders as $order) {
                if (!isset($order['video_id']) || !isset($order['position'])) {
                    throw new Exception('Invalid video order data');
                }
                
                $query = "UPDATE playlist_videos SET position = :position WHERE playlist_id = :playlist_id AND video_id = :video_id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':position', $order['position']);
                $stmt->bindParam(':playlist_id', $playlist_id);
                $stmt->bindParam(':video_id', $order['video_id']);
                $stmt->execute();
            }
            
            $db->commit();
            successResponse(null, 'Playlist videos reordered successfully');
            
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
        
    } catch (Exception $e) {
        errorResponse('Failed to reorder playlist videos: ' . $e->getMessage(), 500);
    }
}

/**
 * Check if video exists in playlist
 */
function checkVideoInPlaylist($db, $playlist_id, $video_id) {
    try {
        $query = "SELECT id FROM playlist_videos WHERE playlist_id = :playlist_id AND video_id = :video_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':playlist_id', $playlist_id);
        $stmt->bindParam(':video_id', $video_id);
        $stmt->execute();
        
        return $stmt->fetch() !== false;
        
    } catch (Exception $e) {
        return false;
    }
}
?>
