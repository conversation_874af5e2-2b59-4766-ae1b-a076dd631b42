<?php
/**
 * Database Connection Test
 * Test if database connection is working and tables exist
 */

// Set CORS headers first
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-User-ID, X-Requested-With");
header("Access-Control-Max-Age: 86400");

// Handle preflight OPTIONS requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit(0);
}

require_once 'config/database.php';

// Set additional CORS headers
setCorsHeaders();

// Get database connection
$database = new Database();
$connection_test = $database->testConnection();

if ($connection_test['status'] === 'error') {
    errorResponse($connection_test['message'], 500);
}

$db = $database->getConnection();

// Test database structure
$tests = [];

// Test 1: Check if database exists and is accessible
$tests['database_connection'] = [
    'name' => 'Database Connection',
    'status' => 'success',
    'message' => 'Connected successfully',
    'details' => $connection_test
];

// Test 2: Check if tables exist
$required_tables = ['users', 'playlists', 'playlist_videos', 'playlist_shares', 'user_favorites'];
$existing_tables = [];

try {
    $query = "SHOW TABLES";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($required_tables as $table) {
        $exists = in_array($table, $tables);
        $existing_tables[$table] = $exists;
    }
    
    $tests['tables'] = [
        'name' => 'Required Tables',
        'status' => array_sum($existing_tables) === count($required_tables) ? 'success' : 'warning',
        'message' => 'Table existence check',
        'details' => $existing_tables
    ];
    
} catch (Exception $e) {
    $tests['tables'] = [
        'name' => 'Required Tables',
        'status' => 'error',
        'message' => 'Failed to check tables: ' . $e->getMessage()
    ];
}

// Test 3: Check if sample data exists
try {
    $query = "SELECT COUNT(*) as count FROM users";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $user_count = $stmt->fetch()['count'];
    
    $query = "SELECT COUNT(*) as count FROM playlists";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $playlist_count = $stmt->fetch()['count'];
    
    $tests['sample_data'] = [
        'name' => 'Sample Data',
        'status' => $user_count > 0 ? 'success' : 'info',
        'message' => 'Data availability check',
        'details' => [
            'users' => $user_count,
            'playlists' => $playlist_count
        ]
    ];
    
} catch (Exception $e) {
    $tests['sample_data'] = [
        'name' => 'Sample Data',
        'status' => 'error',
        'message' => 'Failed to check sample data: ' . $e->getMessage()
    ];
}

// Test 4: Check API endpoints
$api_endpoints = [
    '/api/playlists.php' => 'Playlist management',
    '/api/playlist-videos.php' => 'Playlist video management'
];

$tests['api_endpoints'] = [
    'name' => 'API Endpoints',
    'status' => 'info',
    'message' => 'Available endpoints',
    'details' => $api_endpoints
];

// Test 5: Check PHP extensions
$required_extensions = ['pdo', 'pdo_mysql', 'json'];
$extension_status = [];

foreach ($required_extensions as $ext) {
    $extension_status[$ext] = extension_loaded($ext);
}

$tests['php_extensions'] = [
    'name' => 'PHP Extensions',
    'status' => array_sum($extension_status) === count($required_extensions) ? 'success' : 'error',
    'message' => 'Required PHP extensions',
    'details' => $extension_status
];

// Overall status
$overall_status = 'success';
foreach ($tests as $test) {
    if ($test['status'] === 'error') {
        $overall_status = 'error';
        break;
    } elseif ($test['status'] === 'warning' && $overall_status !== 'error') {
        $overall_status = 'warning';
    }
}

// Response
$response = [
    'overall_status' => $overall_status,
    'timestamp' => date('Y-m-d H:i:s'),
    'php_version' => phpversion(),
    'tests' => $tests
];

// Add setup instructions if needed
if ($overall_status !== 'success') {
    $response['setup_instructions'] = [
        'database' => 'Run the SQL script: database/mysql_playlist_schema.sql',
        'xampp' => 'Make sure XAMPP MySQL service is running',
        'config' => 'Check database configuration in api/config/database.php'
    ];
}

successResponse($response, 'Database test completed');
?>
