# 🔧 CORS Fix untuk API Playlist

## ❌ Error yang <PERSON>
```
Access to fetch at 'http://localhost/sing-along-web-beats/api/playlists' 
from origin 'http://localhost:8080' has been blocked by CORS policy
```

## ✅ <PERSON><PERSON><PERSON> yang Sudah Diterapkan

### 1. Updated CORS Headers di PHP
- Set headers di awal setiap file API
- Handle OPTIONS preflight requests
- Allow semua origins dan methods

### 2. Updated .htaccess
- Set CORS headers di Apache level
- Handle OPTIONS requests dengan rewrite rules

### 3. Files yang Diupdate
- `api/config/database.php` - Improved CORS function
- `api/playlists.php` - Added CORS headers at top
- `api/playlist-videos.php` - Added CORS headers at top
- `api/test-connection.php` - Added CORS headers at top
- `api/.htaccess` - Improved CORS handling

### 4. New Files
- `api/cors.php` - CORS handler
- `api/test-cors.php` - CORS testing endpoint

## 🧪 Test CORS Fix

### 1. Test CORS Endpoint
```bash
curl -X OPTIONS http://localhost/sing-along-web-beats/api/test-cors
```

### 2. Test dari Browser
Buka browser console dan jalankan:
```javascript
fetch('http://localhost/sing-along-web-beats/api/test-cors')
  .then(response => response.json())
  .then(data => console.log('CORS Test:', data))
  .catch(error => console.error('CORS Error:', error));
```

### 3. Test API Playlist
```javascript
fetch('http://localhost/sing-along-web-beats/api/playlists', {
  headers: {
    'X-User-ID': '1'
  }
})
  .then(response => response.json())
  .then(data => console.log('Playlists:', data))
  .catch(error => console.error('API Error:', error));
```

## 🔧 Manual Fix Steps

Jika masih ada masalah CORS:

### 1. Check Apache Modules
Pastikan mod_headers enabled di XAMPP:
```apache
# Di httpd.conf, uncomment line ini:
LoadModule headers_module modules/mod_headers.so
```

### 2. Alternative .htaccess
Jika masih error, ganti isi `.htaccess` dengan:
```apache
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-User-ID"

RewriteEngine On
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ - [R=200,L]

RewriteRule ^playlists/?$ playlists.php [L,QSA]
RewriteRule ^playlist-videos/?$ playlist-videos.php [L,QSA]
RewriteRule ^test/?$ test-connection.php [L,QSA]
```

### 3. PHP Alternative
Jika .htaccess tidak bekerja, tambahkan di awal setiap PHP file:
```php
<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-User-ID");

if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}
?>
```

## 🚀 Quick Test

1. **Restart XAMPP** (Apache & MySQL)
2. **Test CORS**: `http://localhost/sing-along-web-beats/api/test-cors`
3. **Test API**: `http://localhost/sing-along-web-beats/api/test-connection`
4. **Restart React**: `npm run dev`
5. **Test Playlist Tab** di aplikasi

## 📱 Expected Results

### CORS Test Success:
```json
{
  "status": "success",
  "message": "CORS test successful",
  "method": "GET"
}
```

### API Test Success:
```json
{
  "error": false,
  "message": "Database test completed",
  "data": {
    "overall_status": "success"
  }
}
```

### React App:
- Playlist tab bisa dibuka
- Tidak ada CORS error di console
- Bisa create playlist baru

## ❌ Troubleshooting

### Still Getting CORS Error?

1. **Check Apache Error Log**:
   ```
   xampp/apache/logs/error.log
   ```

2. **Check mod_headers**:
   ```apache
   # Di httpd.conf
   LoadModule headers_module modules/mod_headers.so
   ```

3. **Check File Permissions**:
   - Folder `api/` harus readable
   - File `.htaccess` harus readable

4. **Alternative Port**:
   Jika port 80 bentrok, ubah di `httpd.conf`:
   ```apache
   Listen 8080
   ```
   
   Lalu akses: `http://localhost:8080/sing-along-web-beats/api/`

5. **Disable Antivirus/Firewall**:
   Sementara disable untuk testing

## 🎯 Next Steps

Setelah CORS fix:
1. Test create playlist
2. Test add video to playlist
3. Test playlist playback
4. Customize UI sesuai kebutuhan

CORS error sudah diperbaiki dengan multiple layers protection! 🚀
