import { useState, useEffect, useRef } from "react";
import { useSearchParams } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Maximize, Minimize, Moon, Sun } from "lucide-react";
import LyricsUpload from "@/components/LyricsUpload";
import LyricsDisplay from "@/components/LyricsDisplay";
import PlayerControls from "@/components/PlayerControls";
import VoiceRecorder from "@/components/VoiceRecorder";

interface LyricLine {
  time: number;
  text: string;
}

/**
 * Halaman pemutar karaoke dengan fitur lengkap
 * - Video YouTube player
 * - Upload dan tampilan lirik
 * - Mode fullscreen dan gelap
 * - Sinkronisasi lirik dengan video
 * - Rekaman suara pengguna
 */
const Player = () => {
  const [searchParams] = useSearchParams();
  const videoId = searchParams.get('video');
  const videoTitle = searchParams.get('title') || 'Karaoke Video';
  
  const [lyrics, setLyrics] = useState<LyricLine[]>([]);
  const [currentTime, setCurrentTime] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(true);
  const [isPlaying, setIsPlaying] = useState(false);
  
  const playerRef = useRef<HTMLDivElement>(null);
  const intervalRef = useRef<NodeJS.Timeout>();

  // Simulasi waktu video (dalam implementasi nyata, ini akan dari YouTube API)
  useEffect(() => {
    if (isPlaying) {
      intervalRef.current = setInterval(() => {
        setCurrentTime(prev => prev + 0.1);
      }, 100);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isPlaying]);

  const handleLyricsUpload = (uploadedLyrics: LyricLine[]) => {
    setLyrics(uploadedLyrics);
  };

  const toggleFullscreen = () => {
    if (!isFullscreen) {
      if (playerRef.current?.requestFullscreen) {
        playerRef.current.requestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
    setIsFullscreen(!isFullscreen);
  };

  const toggleDarkMode = () => {
    setIsDarkMode(!isDarkMode);
  };

  if (!videoId) {
    return (
      <div className="min-h-screen py-8 px-4 flex items-center justify-center">
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardContent className="p-8 text-center">
            <h2 className="text-white text-2xl mb-4">Video Tidak Ditemukan</h2>
            <p className="text-white/70">Silakan pilih video dari halaman karaoke</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div 
      ref={playerRef}
      className={`min-h-screen transition-colors duration-300 ${
        isDarkMode 
          ? 'bg-gradient-to-br from-gray-900 via-black to-gray-900' 
          : 'bg-gradient-to-br from-blue-100 via-white to-purple-100'
      } ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}
    >
      {/* Header Controls */}
      <div className="flex justify-between items-center p-4">
        <h1 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
          🎤 {videoTitle}
        </h1>
        <div className="flex space-x-2">
          <Button
            onClick={toggleDarkMode}
            variant="outline"
            size="icon"
            className={`${isDarkMode ? 'bg-white/10 border-white/20 text-white' : 'bg-black/10 border-black/20 text-black'}`}
          >
            {isDarkMode ? <Sun className="w-4 h-4" /> : <Moon className="w-4 h-4" />}
          </Button>
          <Button
            onClick={toggleFullscreen}
            variant="outline"
            size="icon"
            className={`${isDarkMode ? 'bg-white/10 border-white/20 text-white' : 'bg-black/10 border-black/20 text-black'}`}
          >
            {isFullscreen ? <Minimize className="w-4 h-4" /> : <Maximize className="w-4 h-4" />}
          </Button>
        </div>
      </div>

      <div className={`container mx-auto px-4 ${isFullscreen ? 'h-[calc(100vh-80px)]' : ''}`}>
        <div className={`grid ${isFullscreen ? 'grid-cols-3 h-full' : 'lg:grid-cols-3'} gap-6`}>
          {/* Video Player */}
          <div className={`${isFullscreen ? 'col-span-2' : 'lg:col-span-2'}`}>
            <Card className={`${isDarkMode ? 'bg-white/10 border-white/20' : 'bg-black/10 border-black/20'} backdrop-blur-md mb-6`}>
              <CardContent className="p-4">
                <div className={`aspect-video rounded-lg overflow-hidden ${isFullscreen ? 'h-[60vh]' : ''}`}>
                  <iframe
                    width="100%"
                    height="100%"
                    src={`https://www.youtube.com/embed/${videoId}?autoplay=1&enablejsapi=1`}
                    title={videoTitle}
                    frameBorder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                    className="w-full h-full"
                  />
                </div>
                
                <PlayerControls 
                  isPlaying={isPlaying}
                  onPlayToggle={() => setIsPlaying(!isPlaying)}
                  currentTime={currentTime}
                  onTimeChange={setCurrentTime}
                  isDarkMode={isDarkMode}
                />
              </CardContent>
            </Card>

            {/* Voice Recorder - Show below video player on larger screens */}
            {!isFullscreen && (
              <VoiceRecorder isDarkMode={isDarkMode} />
            )}
          </div>

          {/* Lyrics and Controls */}
          <div className="space-y-4">
            {/* Lyrics Upload */}
            <LyricsUpload onLyricsUpload={handleLyricsUpload} isDarkMode={isDarkMode} />
            
            {/* Lyrics Display */}
            <LyricsDisplay 
              lyrics={lyrics}
              currentTime={currentTime}
              isDarkMode={isDarkMode}
              isFullscreen={isFullscreen}
            />

            {/* Voice Recorder - Show in sidebar when fullscreen */}
            {isFullscreen && (
              <VoiceRecorder isDarkMode={isDarkMode} />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Player;
