# 🚀 Quick Setup - Playlist YouTube untuk XAMPP

## ⚡ Setup Cepat (5 Menit)

### 1. Start XAMPP
- Buka XAMPP Control Panel
- Start **Apache** dan **MySQL**

### 2. Setup Database
1. Buka phpMyAdmin: `http://localhost/phpmyadmin`
2. Klik tab "SQL"
3. Copy-paste isi file `database/setup_karaoke_db.sql`
4. Klik "Go"

### 3. Copy API Files
Copy folder `api/` ke `htdocs/sing-along-web-beats/`

### 4. Test Setup
Buka: `http://localhost/sing-along-web-beats/api/test-connection.php`

<PERSON><PERSON> berhasil, akan muncul:
```json
{
  "error": false,
  "message": "Database test completed",
  "data": {
    "overall_status": "success"
  }
}
```

## 🔧 Fix Error PlaylistRepeatMode

Error sudah diperbaiki di `src/contexts/PlaylistContext.tsx`. Restart development server:

```bash
npm run dev
# atau
yarn dev
```

## 📱 Test Fitur Playlist

1. <PERSON>uka aplikasi: `http://localhost:5173` (atau port Vite Anda)
2. Klik tab "Karaoke"
3. Klik tab "Playlist"
4. Klik "Buat Playlist"
5. Isi nama playlist dan klik "Buat Playlist"
6. Kembali ke tab "YouTube Karaoke"
7. Cari video dan klik "Tambah ke Playlist"

## 🗄️ Database Info

- **Database**: `karaoke_db`
- **Tables**: `users`, `playlists`, `playlist_videos`
- **Demo User**: ID 1 (username: demo_user)
- **Sample Playlists**: 3 playlist contoh

## 🔗 API Endpoints

Base URL: `http://localhost/sing-along-web-beats/api/`

- `GET /playlists` - Daftar playlist
- `POST /playlists` - Buat playlist baru
- `POST /playlist-videos` - Tambah video ke playlist
- `DELETE /playlist-videos?playlist_id=1&video_id=abc` - Hapus video

## ❌ Troubleshooting

### Error: Database connection failed
```bash
# Check di XAMPP Control Panel:
# - MySQL harus running (hijau)
# - Port 3306 tidak bentrok
```

### Error: PlaylistRepeatMode not defined
```bash
# Restart development server
npm run dev
```

### Error: 404 API Not Found
```bash
# Pastikan struktur folder:
htdocs/sing-along-web-beats/api/playlists.php ✅
```

### Error: CORS
```bash
# Pastikan file .htaccess ada di folder api/
```

## ✅ Checklist Setup

- [ ] XAMPP Apache & MySQL running
- [ ] Database `karaoke_db` created
- [ ] Tables created (users, playlists, playlist_videos)
- [ ] API files copied to htdocs
- [ ] Test connection success
- [ ] React app running without errors
- [ ] Playlist tab visible in Karaoke page

## 🎯 Next Steps

Setelah setup berhasil:
1. Test buat playlist baru
2. Test tambah video ke playlist
3. Test putar playlist
4. Customize sesuai kebutuhan

## 📞 Support

Jika masih ada error:
1. Check console browser (F12)
2. Check XAMPP error logs
3. Pastikan semua file ada di tempat yang benar
4. Restart XAMPP dan development server
