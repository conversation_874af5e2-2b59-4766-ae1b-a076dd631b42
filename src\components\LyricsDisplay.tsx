
import { useEffect, useRef } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface LyricLine {
  time: number;
  text: string;
}

interface LyricsDisplayProps {
  lyrics: LyricLine[];
  currentTime: number;
  isDarkMode: boolean;
  isFullscreen: boolean;
}

/**
 * Komponen untuk menampilkan lirik tersinkronisasi
 * - Highlight lirik yang sedang aktif
 * - Auto scroll ke lirik yang sedang dimainkan
 * - Animasi transisi yang halus
 */
const LyricsDisplay = ({ lyrics, currentTime, isDarkMode, isFullscreen }: LyricsDisplayProps) => {
  const lyricsContainerRef = useRef<HTMLDivElement>(null);
  const activeLyricRef = useRef<HTMLDivElement>(null);

  // Cari lirik yang sedang aktif
  const getCurrentLyricIndex = () => {
    for (let i = lyrics.length - 1; i >= 0; i--) {
      if (currentTime >= lyrics[i].time) {
        return i;
      }
    }
    return -1;
  };

  const currentLyricIndex = getCurrentLyricIndex();

  // Auto scroll ke lirik yang aktif
  useEffect(() => {
    if (activeLyricRef.current && lyricsContainerRef.current) {
      activeLyricRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });
    }
  }, [currentLyricIndex]);

  if (lyrics.length === 0) {
    return (
      <Card className={`${isDarkMode ? 'bg-white/10 border-white/20' : 'bg-black/10 border-black/20'} backdrop-blur-md`}>
        <CardHeader>
          <CardTitle className={`${isDarkMode ? 'text-white' : 'text-gray-900'} text-lg`}>
            Lirik
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className={`${isDarkMode ? 'text-white/60' : 'text-gray-600'}`}>
              Upload file lirik untuk melihat teks tersinkronisasi
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`${isDarkMode ? 'bg-white/10 border-white/20' : 'bg-black/10 border-black/20'} backdrop-blur-md ${isFullscreen ? 'h-[35vh]' : 'h-80'}`}>
      <CardHeader>
        <CardTitle className={`${isDarkMode ? 'text-white' : 'text-gray-900'} text-lg`}>
          Lirik
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div 
          ref={lyricsContainerRef}
          className={`${isFullscreen ? 'h-[25vh]' : 'h-60'} overflow-y-auto space-y-3 pr-2 scrollbar-thin ${isDarkMode ? 'scrollbar-thumb-white/20' : 'scrollbar-thumb-black/20'}`}
        >
          {lyrics.map((lyric, index) => (
            <div
              key={index}
              ref={index === currentLyricIndex ? activeLyricRef : null}
              className={`transition-all duration-300 p-2 rounded-lg ${
                index === currentLyricIndex
                  ? `${isDarkMode ? 'bg-gradient-to-r from-pink-500/30 to-purple-500/30 text-white scale-105' : 'bg-gradient-to-r from-pink-500/20 to-purple-500/20 text-gray-900 scale-105'} shadow-lg`
                  : index < currentLyricIndex
                  ? `${isDarkMode ? 'text-white/40' : 'text-gray-400'}`
                  : `${isDarkMode ? 'text-white/70' : 'text-gray-700'}`
              }`}
            >
              <div className="flex items-center space-x-3">
                <span className={`text-xs font-mono ${isDarkMode ? 'text-white/50' : 'text-gray-500'}`}>
                  {Math.floor(lyric.time / 60).toString().padStart(2, '0')}:
                  {Math.floor(lyric.time % 60).toString().padStart(2, '0')}
                </span>
                <p className={`text-sm ${index === currentLyricIndex ? 'font-semibold' : ''} leading-relaxed`}>
                  {lyric.text}
                </p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default LyricsDisplay;
