
import { useEffect, useRef } from "react";

interface WaveVisualizerProps {
  isRecording: boolean;
  stream: MediaStream | null;
  isDarkMode: boolean;
}

/**
 * Komponen visualisasi gelombang suara real-time
 * - Menggunakan Web Audio API untuk analisis frekuensi
 * - Menampilkan bar chart animasi sesuai input audio
 * - Responsive terhadap tema gelap/terang
 */
const WaveVisualizer = ({ isRecording, stream, isDarkMode }: WaveVisualizerProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const dataArrayRef = useRef<Uint8Array | null>(null);

  useEffect(() => {
    if (isRecording && stream) {
      setupAudioContext();
    } else {
      cleanup();
    }

    return cleanup;
  }, [isRecording, stream]);

  const setupAudioContext = () => {
    try {
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const source = audioContext.createMediaStreamSource(stream!);
      const analyser = audioContext.createAnalyser();
      
      analyser.fftSize = 256;
      const bufferLength = analyser.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);
      
      source.connect(analyser);
      
      audioContextRef.current = audioContext;
      analyserRef.current = analyser;
      dataArrayRef.current = dataArray;
      
      draw();
    } catch (error) {
      console.error('Error setting up audio context:', error);
    }
  };

  const cleanup = () => {
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }
    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }
  };

  const draw = () => {
    if (!analyserRef.current || !dataArrayRef.current || !canvasRef.current) {
      return;
    }

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const analyser = analyserRef.current;
    const dataArray = dataArrayRef.current;
    const bufferLength = analyser.frequencyBinCount;

    analyser.getByteFrequencyData(dataArray);

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Calculate bar width
    const barWidth = (canvas.width / bufferLength) * 2.5;
    let barHeight;
    let x = 0;

    // Draw bars
    for (let i = 0; i < bufferLength; i++) {
      barHeight = (dataArray[i] / 255) * canvas.height * 0.8;

      // Create gradient based on theme
      const gradient = ctx.createLinearGradient(0, canvas.height - barHeight, 0, canvas.height);
      if (isDarkMode) {
        gradient.addColorStop(0, '#ec4899'); // pink-500
        gradient.addColorStop(1, '#8b5cf6'); // purple-500
      } else {
        gradient.addColorStop(0, '#f97316'); // orange-500
        gradient.addColorStop(1, '#06b6d4'); // cyan-500
      }

      ctx.fillStyle = gradient;
      ctx.fillRect(x, canvas.height - barHeight, barWidth, barHeight);

      x += barWidth + 1;
    }

    animationRef.current = requestAnimationFrame(draw);
  };

  return (
    <div className="flex items-center justify-center p-4">
      <canvas
        ref={canvasRef}
        width={300}
        height={100}
        className={`rounded-lg ${isDarkMode ? 'bg-black/20' : 'bg-white/20'} border ${isDarkMode ? 'border-white/20' : 'border-black/20'}`}
      />
      {!isRecording && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className={`text-sm ${isDarkMode ? 'text-white/50' : 'text-gray-500'}`}>
            Mulai rekam untuk melihat visualisasi
          </div>
        </div>
      )}
    </div>
  );
};

export default WaveVisualizer;
